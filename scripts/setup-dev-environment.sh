#!/bin/bash

# Development Environment Setup Script
# This script sets up the development environment including Git hooks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 Setting up development environment..."
echo "========================================"

# Check if we're in the right directory
if [ ! -f "pom.xml" ]; then
    print_error "This doesn't appear to be the project root directory!"
    print_error "Please run this script from the project root."
    exit 1
fi

# Check Java version
print_status "Checking Java version..."
if command -v java &> /dev/null; then
    java_version=$(java -version 2>&1 | head -1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$java_version" -ge 17 ]; then
        print_success "Java $java_version detected ✓"
    else
        print_warning "Java 17+ recommended. Current version: $java_version"
    fi
else
    print_error "Java not found! Please install Java 17+."
    exit 1
fi

# Check Maven
print_status "Checking Maven..."
if command -v mvn &> /dev/null; then
    mvn_version=$(mvn -version | head -1 | cut -d' ' -f3)
    print_success "Maven $mvn_version detected ✓"
elif [ -f "./mvnw" ]; then
    print_success "Maven wrapper found ✓"
else
    print_error "Maven not found! Please install Maven or ensure mvnw is present."
    exit 1
fi

# Setup Git hooks
print_status "Setting up Git hooks..."
if [ -f ".git/hooks/pre-commit" ]; then
    chmod +x .git/hooks/pre-commit
    print_success "Pre-commit hook enabled ✓"
else
    print_error "Pre-commit hook not found!"
    exit 1
fi

if [ -f ".git/hooks/commit-msg" ]; then
    chmod +x .git/hooks/commit-msg
    print_success "Commit message hook enabled ✓"
else
    print_warning "Commit message hook not found"
fi

# Test Maven build
print_status "Testing Maven build..."
maven_cmd="mvn"
if [ -f "./mvnw" ]; then
    maven_cmd="./mvnw"
fi

if $maven_cmd clean compile -q; then
    print_success "Maven build test passed ✓"
else
    print_error "Maven build failed! Please check your setup."
    exit 1
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p logs
mkdir -p target
print_success "Directories created ✓"

# Test Git hooks
print_status "Testing Git hooks..."
if .git/hooks/pre-commit; then
    print_success "Pre-commit hook test passed ✓"
else
    print_warning "Pre-commit hook test failed - you may need to fix compilation issues first"
fi

echo "========================================"
print_success "🎉 Development environment setup complete!"
echo ""
echo "Next steps:"
echo "1. Run 'mvn clean install' to build the project"
echo "2. Run 'mvn spring-boot:run' to start the application"
echo "3. Check 'docs/git-hooks.md' for Git hooks documentation"
echo ""
echo "Useful commands:"
echo "  ./scripts/manage-git-hooks.sh status  # Check Git hooks status"
echo "  ./scripts/manage-git-hooks.sh test    # Test Git hooks"
echo "  mvn clean install                     # Full build"
echo "  mvn spring-boot:run                   # Start application"
echo ""
