package com.arealytics.core.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UseType implements BaseEnum {
    RETAIL(2, "Retail"),
    INDUSTRIAL(3, "Industrial"),
    APARTMENTS(4, "Apartments"),
    OFFICE(5, "Office"),
    LAND(7, "Land"),
    SPECIAL_USE(9, "Special Use");

    private final Integer id;
    private final String label;

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static UseType fromId(int id) {
        for (UseType pt : values()) {
            if (pt.id == id) {
                return pt;
            }
        }
        throw new IllegalArgumentException("Invalid UseType ID: " + id);
    }
}
