package com.arealytics.core.enumeration;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum ClassType implements BaseEnum {
    A(1, "A", UseType.OFFICE),
    B(2, "B", UseType.OFFICE),
    C(3, "C", UseType.OFFICE),
    PRE(4, "Pre", UseType.OFFICE),
    PRIME(5, "Prime", UseType.INDUSTRIAL),
    SECONDARY(6, "Secondary", UseType.INDUSTRIAL);

    private final int id;
    private final String label;
    private final UseType type;

    ClassType(int id, String label, UseType type) {
        this.id = id;
        this.label = label;
        this.type = type;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public UseType getType() {
        return type;
    }

    public static ClassType fromId(int id) {
        for (ClassType type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid ClassType ID: " + id);
    }
}
