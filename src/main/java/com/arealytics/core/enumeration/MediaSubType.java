package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum MediaSubType implements BaseEnum {
  MAIN_PHOTO(1, "Main Photo"),
  RIGHT_SIDE(2, "Right Side"),
  LOBBY(3, "Lobby"),
  REAR(4, "Rear"),
  LEFT_SIDE(5, "Left Side"),
  OUT_BUILDING(6, "Out Building"),
  FRONT(7, "Front"),
  MISC(8, "Misc");

  private final int id;
  private final String name;

  public static MediaSubType fromId(int id) {
    for (MediaSubType loc : values()) {
      if (loc.getId() == id)
        return loc;
    }
    throw new IllegalArgumentException("Invalid MediaLocation ID: " + id);
  }
}
