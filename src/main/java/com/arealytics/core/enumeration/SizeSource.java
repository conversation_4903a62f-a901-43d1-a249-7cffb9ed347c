package com.arealytics.core.enumeration;

public enum SizeSource implements BaseEnum {
    AERIAL_ESTIMATION(1, "Aerial Estimation"),
    APPRAISER(3, "Appraiser"),
    BROKER_AGENT(4, "Broker/Agent"),
    COUNTY_DATA_SOURCE(6, "County Data Source"),
    PROPERTY_OWNER(9, "Property Owner"),
    BROCHURE(10, "Brochure"),
    THIRD_PARTY(13, "Third Party"),
    ONSITE_MEASUREMENT(15, "Onsite Measurement");

    private final int id;
    private final String label;

    SizeSource(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static SizeSource fromId(int id) {
        for (SizeSource s : values()) {
            if (s.id == id) {
                return s;
            }
        }
        throw new IllegalArgumentException("Invalid SizeSource ID: " + id);
    }
}
