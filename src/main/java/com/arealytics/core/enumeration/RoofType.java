package com.arealytics.core.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum RoofType {
    BUILT_UP(1, "Built-Up"),
    COMPOSITION(2, "Composition"),
    HIP(3, "Hip"),
    METAL_ROOF(4, "Metal Roof"),
    TPO(5, "Thermoplastic (TPO)"),
    EPDM(6, "EPDM"),
    SOLAR_PANELS(7, "PV Solar Panels"),
    MODIFIED_BITUMEN(8, "Modified Bitumen"),
    SINGLE_PLY(9, "Single Ply"),
    OTHER(10, "Other");

    private final int id;
    private final String label;
}
