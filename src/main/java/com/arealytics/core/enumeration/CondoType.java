package com.arealytics.core.enumeration;

public enum CondoType implements BaseEnum {
    NOT_STRATA(1, "Not Strata"),
    STRATA(2, "Strata"),
    MASTER_STRATA_RECORD(3, "Master Strata Record"),
    MASTER_FREEHOLD(4, "Master Freehold"),
    CHILD_FREEHOLD(5, "Child Freehold");

    private final int id;
    private final String label;

    CondoType(int id, String label) {
        this.id = id;
        this.label = label;
    }

    @Override
    public int getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public static CondoType fromId(int id) {
        for (CondoType type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid CondoType ID: " + id);
    }
}
