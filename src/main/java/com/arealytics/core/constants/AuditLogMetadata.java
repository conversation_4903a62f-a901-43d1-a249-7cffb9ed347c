package com.arealytics.core.constants;

import java.util.Map;

import com.arealytics.core.domain.empiricalProd.Parcel;

public class AuditLogMetadata {

    // entity-specific data (class, ID field, and change log fields mapping)
    public static final Map<String, EntityMetadata> ENTITY_METADATA_MAP = Map.of(
        "Parcel", new EntityMetadata(
            Parcel.class, 
            "ParcelID", 
            Map.of(
                "parcelSize", "ParcelSF",
                "parcelNo", "ParcelNo",
                "lot", "Lot",
                "block", "Block",
                "subDivision", "Subdivision"
            )
        )
    );

    // Get the metadata for a given entity type
    public static EntityMetadata getEntityMetadata(String entityType) {
        return ENTITY_METADATA_MAP.get(entityType);
    }

    // EntityMetadata class to hold the relevant data
    public static class EntityMetadata {
        private final Class<?> entityClass;
        private final String idField;
        private final Map<String, String> changeLogFieldsMapping;

        public EntityMetadata(Class<?> entityClass, String idField, Map<String, String> changeLogFieldsMapping) {
            this.entityClass = entityClass;
            this.idField = idField;
            this.changeLogFieldsMapping = changeLogFieldsMapping;
        }

        public Class<?> getEntityClass() {
            return entityClass;
        }

        public String getIdField() {
            return idField;
        }

        public Map<String, String> getChangeLogFieldsMapping() {
            return changeLogFieldsMapping;
        }
    }
}
