package com.arealytics.core.converter;

import java.lang.reflect.ParameterizedType;
import java.util.Arrays;

import com.arealytics.core.enumeration.BaseEnum;

import jakarta.persistence.AttributeConverter;

public abstract class GenericEnumConverter<E extends Enum<E> & BaseEnum>
        implements AttributeConverter<E, Integer> {

    private final Class<E> enumClass;

    @SuppressWarnings("unchecked")
    public GenericEnumConverter() {
        this.enumClass =
                (Class<E>)
                        ((ParameterizedType) getClass().getGenericSuperclass())
                                .getActualTypeArguments()[0];
    }

    @Override
    public Integer convertToDatabaseColumn(E attribute) {
        return attribute != null ? attribute.getId() : null;
    }

    @Override
    public E convertToEntityAttribute(Integer dbData) {
        if (dbData == null) return null;

        return Arrays.stream(enumClass.getEnumConstants())
                .filter(e -> e.getId() == dbData)
                .findFirst()
                .orElseThrow(
                        () ->
                                new IllegalArgumentException(
                                        "Invalid " + enumClass.getSimpleName() + " ID: " + dbData));
    }
}
