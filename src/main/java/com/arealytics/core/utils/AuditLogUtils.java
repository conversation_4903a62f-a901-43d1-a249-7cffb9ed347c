package com.arealytics.core.utils;

import com.arealytics.core.constants.AuditLogConstants;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

public class AuditLogUtils {

    public static Map<String, Object> createAuditLogEntry(
            Integer entityId,
            String idFieldName,
            String displayTextForFieldName,
            String changeLogFieldName,
            Object oldValue,
            Object newValue,
            Instant timestamp,
            String action,
            String changedBy,
            String applicationName) {

        Map<String, Object> logEntry = new HashMap<>();
        logEntry.put(idFieldName, entityId);
        logEntry.put(AuditLogConstants.CHANGED_DATE, timestamp);
        logEntry.put(AuditLogConstants.CHANGED_BY, changedBy);
        logEntry.put(AuditLogConstants.ACTION, action);
        logEntry.put(AuditLogConstants.FIELD_NAME, changeLogFieldName);
        logEntry.put(AuditLogConstants.OLD_VALUE, oldValue);
        logEntry.put(AuditLogConstants.NEW_VALUE, newValue);
        logEntry.put(AuditLogConstants.DISPLAY_TEXT, displayTextForFieldName);
        logEntry.put(AuditLogConstants.APPLICATION_DESCRIPTION, applicationName);
        return logEntry;
    }
}

