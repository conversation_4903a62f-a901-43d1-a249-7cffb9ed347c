package com.arealytics.core.utils;

import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.core.types.dsl.NumberPath;

import java.math.BigDecimal;
import java.math.RoundingMode;

public final class UnitConversionUtil {
    public static final double SQFT_TO_SQM_FACTOR = 0.092903;
    private static final double SQM_TO_SQFT_FACTOR = 1 / SQFT_TO_SQM_FACTOR;

    private UnitConversionUtil() {
        throw new UnsupportedOperationException("Utility class should not be instantiated");
    }

    public static double sqftToSqm(double sqft) {
        return round(sqft * SQFT_TO_SQM_FACTOR);
    }

    public static NumberExpression<BigDecimal> sqftToSqmConversion(NumberPath<BigDecimal> sqft) {
        return sqft.multiply(SQFT_TO_SQM_FACTOR);
    }

    public static double sqmToSqft(double sqm) {
        return round(sqm * SQM_TO_SQFT_FACTOR);
    }

    private static double round(double value) {
        return BigDecimal.valueOf(value).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }
}
