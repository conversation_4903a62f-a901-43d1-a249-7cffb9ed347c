package com.arealytics.core.dto.querydsl;

import com.arealytics.core.dto.response.AddressDTO;
import com.arealytics.core.dto.response.BaseDTO;
import com.arealytics.core.dto.response.LocationDTO;
import com.arealytics.core.enumeration.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertyDetailsQuerydslDTO {
    private Integer propertyID;

    private String propertyName;

    private Integer parentPropertyID;

    private Boolean isActive;

    private Integer yearBuilt;

    private Integer yearRenovated;

    private Integer floors;

    private ConstructionStatus constructionStatusID;

    private ConstructionType constructionTypeID;

    private HVACType hvacTypeID;

    private SprinklerType sprinklerTypeID;

    private CondoType condoTypeID;

    private String CondoUnit;

    private Integer useTypeID;

    private String useTypeName;

    private Integer specificUseID;

    private String specificUseName;

    private LocalDate estimatedCompletionDate;

    private Boolean isADAAccessible;

    private Boolean isVented;

    private Boolean isOwnerOccupied;

    private ClassType classTypeID;

    private Tenancy tenancyTypeID;

    private Boolean isEnergyStar;

    private SizeSource NRASizeSourceID;

    private String mixedUseAllocation;

    private String buildingWebsite;

    private String buildingComments;

    @JsonProperty("NoOfOfficeFloor")
    private Integer noOfOfficeFloors;

    private Integer metroId;

    private Integer marketId;

    private Integer subMarketID;

    private GovernmentInterest governmentInterestID;

    private Boolean hasSolar;

    private Integer trafficCount;

    private EnergyStarRating energyStarRatingID;

    private WaterStarRating waterStarRatingID;

    private GreenStarRating greenStarRatingID;

    @JsonProperty("OccupancyPercent")
    private BigDecimal occupiedPercentage;

    @JsonProperty("OccupiedPercentage")
    private BigDecimal directOccupiedPercentage;

    private String currentTitle;

    private BigDecimal tiAllowance;

    @JsonProperty("GRESBScore")
    private BigDecimal gresbScore;

    @JsonProperty("GRESBScoreMin")
    private Integer gresbScoreMin;

    @JsonProperty("GRESBScoreMax")
    private Integer gresbScoreMax;

    private LocalDateTime actualCompletion;

    private LocalDateTime titleReferenceDate;

    private Integer landUse;

    private Integer lastReviewedBy;

    private LocalDateTime lastReviewedDate;

    private LocalDateTime constructionStartDate;

    @JsonProperty("EstCompletionDate")
    private LocalDateTime estCompletion;

    private Integer bookValue;

    private LocalDateTime bookValueDate;

    private String parcelInfo;

    private String mainPhotoUrl;

    @JsonProperty("Complex")
    private String parkComplexName;

    private String zoning;

    @JsonProperty("LGA")
    private String lga;

    private Boolean useAddressAsPropertyName;

    private String amenities;

    private Integer passengerElevators;

    private Integer parkingElevators;

    private Integer freighElevators;

    private Integer dockHigh;

    @JsonProperty("GradeLevelDriveIn")
    private Integer gradeLevelIn;

    private Integer truckwell;

    private Boolean yardPaved;

    @JsonProperty("ReservedParkingSpaces")
    private Integer reservedParkingSpaces;

    @JsonProperty("ReservedParkingSpacesRatePerMonth")
    private BigDecimal reservedParkingSpacesRatePerMonth;

    @JsonProperty("HasReservedParkingSpaces")
    private Boolean hasReservedParkingSpaces;

    @JsonProperty("UnreservedParkingSpaces")
    private Integer unreservedParkingSpaces;

    @JsonProperty("UnreservedParkingSpacesRatePerMonth")
    private BigDecimal unreservedParkingSpacesRatePerMonth;

    @JsonProperty("HasUnreservedParkingSpaces")
    private Boolean hasUnreservedParkingSpaces;

    private String powerComments;

    private String utilityComments;

    private String propertyComments;

    private Integer noOfUnits;

    @JsonProperty("TotalAnchorSF")
    private BigDecimal totalAnchor;

    @JsonProperty("HVAC")
    private Boolean hvac;

    private Boolean lifts;

    private Integer liftsCount;

    private Integer powerType;

    private Integer vacancy;

    private BigDecimal bayWidth;

    private BigDecimal bayDepth;

    private BigDecimal depth;

    private BigDecimal width;

    private Boolean includeInAnalytics;

    private String GBASizeSource;

    private HVACType officeAC;

    private SizeSource lotSizeSourceID;

    private ZoningClass zoningClassID;

    private String zoningCode;

    private ZoningClass potentialZoningID;

    private ZoningClass surroundingLandUse;

    private Boolean railServed;

    private Boolean isFloodPlain;

    private Integer smallestFloor;

    private Integer largestFloor;

    private RoofType roofTypeID;

    @JsonProperty("IsCraneServed")
    private Boolean craneServed;

    private Boolean hasYardFenced;

    private Boolean hasYardUnfenced;

    private BuildSpecStatus buildSpecStatusID;

    private String legalDescription;

    private String internalComments;

    private Integer parkingSpaces;

    private Boolean hasSprinkler;

    private SizeSource sizeSourceID;

    @JsonProperty("Anchors")
    private Integer noOfAnchor;

    private Boolean hasPortAccess;

    private Boolean hasYard;

    private Boolean hasResCoveredParking;

    private String parkingRatio;

    private Integer earthquakeZoneID;

    private String geoscapePropertyID;

    private String counsilTaxID;

    private String valuerGeneralID;

    private BigDecimal clearHeightMin;

    private BigDecimal clearHeightMax;

    private Integer volts;

    private Integer phase;

    private Integer amps;

    private BigDecimal retailFrontage;

    private BigDecimal typicalFloorSize;

    private BigDecimal hardstandArea;

    private SizeSource hardstandAreaSourceID;

    private SizeSource contributedGBASizeSourceID;

    @JsonProperty("GLASizeSourceID")
    private SizeSource glasSizeSourceID;

    @JsonProperty("GLARSizeSourceID")
    private SizeSource glarSizeSourceID;

    private Boolean mezzanine;

    private Boolean awnings;

    private Integer awningsCount;

    private BigDecimal buildingSize;

    @JsonProperty("LotSizeAcres")
    private BigDecimal lotSize;

    private BigDecimal minFloorSize;

    private BigDecimal maxFloorSize;

    @JsonProperty("RetailSF")
    private BigDecimal retailSize;

    @JsonProperty("OfficeSF")
    private BigDecimal officeSize;

    @JsonProperty("BuildingSF")
    private BigDecimal buildingSizeSF;

    @JsonProperty("LotSizeSF")
    private BigDecimal lotSizeSF;

    @JsonProperty("TotalSaleSizeSF")
    private BigDecimal totalSaleSizeSF;

    @JsonProperty("ContributedGBA_SF")
    private BigDecimal contributedGBASizeSF;

    @JsonProperty("GLA_SF")
    private BigDecimal glaSizeSF;

    @JsonProperty("GLAR_SF")
    private BigDecimal glarSizeSF;

    @JsonProperty("Mezzanine_Size_SF")
    private BigDecimal mezzanineSizeSF;

    @JsonProperty("Awnings_Size_SF")
    private BigDecimal awningsSizeSF;

    @JsonProperty("LotSizeAc")
    private BigDecimal lotSizeAC;

    @JsonProperty("NLA_SF")
    private BigDecimal nla;

    @JsonProperty("NLAac")
    private BigDecimal nlaac;

    @JsonProperty("ContributedGBA_SM")
    private BigDecimal contributedGBA;

    @JsonProperty("GLA")
    private BigDecimal gla;

    @JsonProperty("GLAR")
    private BigDecimal glar;

    @JsonProperty("PropertyResearchTypeID")
    private Integer researchTypeID;

    private String researchTypeName;

    private String trueOwners;

    private String recordedOwners;

    private Boolean isSkipped;

    private Boolean isMultiplePolygonsNeeded;

    private String needsResearchComments;

    private Boolean hasNoBuildingFootprints;

    private Boolean hasNoExistingParcelInTileLayer;

    private Integer masterPropertyId;

    private String propertyCreatedByName;

    private String propertyModifiedByName;

    private String propertyLastReviewedByName;

    private  String contributedSourceComments;

    @JsonProperty("TypicalFloorSizeSourceID")
    private Integer typicalFloorSizeSourceId;

    @JsonProperty("OfficeHVAC")
    private OfficeHvac officeHvac;

    @JsonProperty("FeatureIDs")
    private String featureIDs;

    @JsonProperty("AmenitiesType")
    private String amenitiesType;

    @JsonProperty("Address")
    private AddressDTO address;

    @JsonProperty("Location")
    private LocationDTO locationFields;
}
