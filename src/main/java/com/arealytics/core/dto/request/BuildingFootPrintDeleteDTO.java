package com.arealytics.core.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@RequiredArgsConstructor
public class BuildingFootPrintDeleteDTO {
    @NotNull(message = "propertyId is required") private Integer propertyId;

    @NotBlank(message = "buildingFootPrintID/s is required") private String buildingFootPrintIDs;
}
