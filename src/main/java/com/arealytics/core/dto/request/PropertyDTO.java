package com.arealytics.core.dto.request;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.arealytics.core.enumeration.*;

import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PropertyDTO {

    private Integer propertyID;
    private String propertyName;
    private Integer parentPropertyID;
    private Boolean isActive;

    // entity
    private Integer entityId;
    private Integer createdBy;
    private Integer modifiedBy;


    // property details
    private Integer yearBuilt;
    private Integer yearRenovated;
    private Integer floors;
    private ConstructionStatus constructionStatusID;
    private ConstructionType constructionTypeID;
    private HVACType hvacTypeID;
    private SprinklerType sprinklerTypeID;
    private CondoType condoTypeID;
    private Integer useTypeID;
    private String useTypeName;
    private Integer specificUseID;
    private String specificUseName;
    private Boolean isADAAccessible;
    private Boolean isVented;
    private Boolean isOwnerOccupied;
    private ClassType classTypeID;
    private Tenancy tenancyTypeID;
    private Boolean isEnergyStar;
    private String mixedUseAllocation;
    private String buildingWebsite;
    private String buildingComments;
    private GovernmentInterest governmentInterestID;
    private Integer noOfOfficeFloors;
    private Boolean craneServed;
    private Integer roofTypeID;
    private Boolean hasSolar;
    private Integer trafficCount;
    private Integer earthquakeZoneID;
    private Date deletedAt;
    private EnergyStarRating energyStarRatingID;
    private WaterStarRating waterStarRatingID;
    private GreenStarRating greenStarRatingID;
    private BigDecimal occupiedPercentage;
    private BigDecimal directOccupiedPercentage;
    private String currentTitle;
    private BigDecimal tiAllowance;
    private BigDecimal gresbScore;
    private Integer gresbScoreMin;
    private Integer gresbScoreMax;
    private Date actualCompletion;
    private Date titleReferenceDate;
    private Integer landUse;
    private Integer lastReviewedBy;
    private Date lastReviewedDate;
    private Date constructionStartDate;
    private Date estCompletion;
    private Date estimatedCompletionDate;
    private Integer bookValue;
    private Date bookValueDate;
    private BigDecimal minAskingSalePrice;
    private BigDecimal maxAskingSalePrice;
    private BigDecimal askingLeaseRatePerYearMin;
    private BigDecimal askingLeaseRatePerYearMax;
    private String parcelInfo;
    private String propertyKey;
    private String mainPhotoUrl;
    private String listingCompany;

    // property location
    private Integer metroId;
    private Integer marketId;
    private String parkComplexName;
    private String address;
    private String zoning;
    private ZoningClass zoningClassID;
    private String zoningCode;
    private String lga;
    private String geoscapePropertyID;
    private String counsilTaxID;
    private String valuerGeneralID;
    private ZoningClass surroundingLandUse;
    private String legalDescription;
    private Boolean useAddressAsPropertyName;

    // property amenities
    private String amenities;
    private Integer parkingSpaces;
    private String parkingRatio;
    private Integer passengerElevators;
    private Integer parkingElevators;
    private Integer freighElevators;
    private Integer dockHigh;
    private Integer gradeLevelIn;
    private Integer truckWell;
    private Integer phase;
    private Integer volts;
    private Integer amps;
    private String powerComments;
    private BigDecimal bayWidth;
    private BigDecimal bayDepth;
    private Boolean includeInAnalytics;
    private HVACType officeAC;
    private ZoningClass potentialZoningID;
    private Boolean railServed;
    private Boolean isFloodPlain;
    private BuildSpecStatus buildSpecStatusID;
    private Boolean hasYardFenced;
    private Boolean hasYardUnfenced;
    private Boolean yardPaved;
    private Boolean hasResCoveredParking;
    private Integer reservedParkingSpaces;
    private BigDecimal reservedParkingSpacesRatePerMonth;
    private Boolean hasReservedParkingSpaces;
    private BigDecimal depth;
    private BigDecimal width;
    private String internalComments;
    private Integer noOfAnchor;
    private Boolean adding;
    private Boolean hasSprinkler;
    private Boolean hasPortAccess;
    private Boolean hasYard;
    private Integer unreservedParkingSpaces;
    private BigDecimal unreservedParkingSpacesRatePerMonth;
    private Boolean hasUnreservedParkingSpaces;
    private String propertyComments;
    private String utilityComments;
    private Integer noOfUnits;
    private BigDecimal totalAnchor;
    private Boolean hvac;
    private Boolean lifts;
    private Integer liftsCount;
    private Integer powerType;
    private Integer vacancy;
    private Integer lCount;
    private Integer llCount;
    private Integer dlCount;
    private Integer slCount;

    // property size related

    private SizeSource sizeSourceID;
    private BigDecimal clearHeightMin;
    private BigDecimal clearHeightMax;
    private BigDecimal retailFrontage;
    private BigDecimal typicalFloorSize;
    private BigDecimal hardstandArea;
    private Integer hardstandAreaSourceID;
    private Integer contributedGBASizeSourceID;
    private Integer glasSizeSourceID;
    private Integer glarSizeSourceID;
    private Boolean mezzanine;
    private Boolean awnings;
    private Integer awningsCount;
    private BigDecimal buildingSize;
    private BigDecimal lotSize;
    private BigDecimal minFloorSize;
    private BigDecimal maxFloorSize;
    private BigDecimal officeSize;
    private BigDecimal retailSize;
    private BigDecimal totalAvailableSF;
    private BigDecimal buildingSizeSF;
    private BigDecimal lotSizeSF;
    private BigDecimal totalSaleSizeSF;
    private BigDecimal contributedGBASizeSF;
    private BigDecimal glaSizeSF;
    private BigDecimal glarSizeSF;
    private BigDecimal mezzanineSizeSF;
    private BigDecimal awningsSizeSF;
    private BigDecimal lotSizeAC;
    private String GBASizeSource;
    private Integer lotSizeSourceID;
    private SizeSource NRASizeSourceID;
    private Integer smallestFloor;
    private Integer largestFloor;
    private BigDecimal nla;
    private BigDecimal nlaac;
    private BigDecimal contributedGBA;
    private BigDecimal gla;
    private BigDecimal glar;

    // property internal tool fields
    private Integer researchTypeID;
    private String researchTypeName;
    private String trueOwners;
    private String recordedOwners;
    private Boolean isSkipped;
    private Boolean isMultiplePolygonsNeeded;
    private String needsResearchComments;

    // fields from Location Table
    private BigDecimal latitude;
    private BigDecimal longitude;
    private BigDecimal ZCoordinate;
    private RoofTopSource rooftopSourceID;
    private Integer gisShapeID;

    // fields from Address Table
    private AddressType addressTypeID;
    private Integer parentID;
    private ParentTable parentTableId;
    private String address1;
    private String address2;
    private Integer stateID;
    private Integer cityID;
    private String zipCode;
    private String addressText;
    private String addressStreetNumber;
    private String addressStreetName;
    //    private Integer suffixID;
    //    private Integer suffix2ID;
    private Integer countyID;

    private String streetNumberMin;
    private String streetNumberMax;
    private String zip4;
    private String floorNumber;

    //    private Integer prefixID;
    //    private Integer prefix2ID;

    private Quadrant quadrantID;
    private String buildingNumber;
    private Integer partOfCenterComplex;
    private String complexName;

    private String primaryStreet;
    private String primaryAccess;
    private String primaryTrafficCount;
    private LocalDateTime primaryTrafficCountDate;
    private String primaryFrontage;

    private String secondaryStreet;
    private String secondaryAccess;
    private String secondaryTrafficCount;
    private LocalDateTime secondaryTrafficCountDate;
    private String secondaryFrontage;

    private Integer sequence;
    private String eastWestSt;
    private String northSouthSt;

    private Integer countryId;
    private Boolean isIntersection;
    private Integer streetNumberMinN;
    private Integer streetNumberMaxN;

    private Integer marketStateID;

    // property strata relationship
    private Integer masterPropertyID;
    private Integer strataPropertyID;
}
