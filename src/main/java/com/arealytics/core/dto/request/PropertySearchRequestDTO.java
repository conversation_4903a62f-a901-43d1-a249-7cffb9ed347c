package com.arealytics.core.dto.request;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

import com.arealytics.core.enumeration.ClassType;
import com.arealytics.core.enumeration.CondoType;
import com.arealytics.core.enumeration.ConstructionStatus;
import com.arealytics.core.enumeration.Tenancy;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
// @Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PropertySearchRequestDTO {
    private List<Integer> propertyTypes;
    private String listingType;
    private List<Integer> cityIds;
    private List<String> zipCodes;
    private List<Integer> specificUseIds;
    private BigDecimal buildingSizeMin;
    private BigDecimal buildingSizeMax;
    private List<ClassType> buildingClasses;
    private String propertyName;
    private String streetMin;
    private String streetMax;
    private String streetName;
    private Integer page = 0;
    private Integer pageSize = 500;
    private String sortBy;
    private String sortDirection;
    private Integer propertyId;
    private List<Integer> propertyIds;
    private List<Tenancy> tenancy;
    private BigDecimal lotSizeSFMin;
    private BigDecimal lotSizeSFMax;
    private Boolean showOnlyListingsAssignedToMe;
    private Integer strataFilter;
    private List<ConstructionStatus> constructionStatus;
    private Instant modifiedDateMin;
    private Instant modifiedDateMax;
    private Integer stateID;
    private Instant createdDateMin;
    private Instant createdDateMax;
    private Integer loginEntityId;
    private Boolean isSkipped;
    private List<Integer> researchStatusIds;
    private List<CondoType> strataTypeIds;
    private Boolean excludeHidden;
    private List<Integer> researcherIds;
    private List<Integer> auditStatus;
    private Boolean isNotReviewed;
    private LocalDateTime lastReviewedDateMin;
    private LocalDateTime lastReviewedDateMax;
    private Boolean hasNoExistingParcelInTileLayer;
    private Boolean hasNoBuildingFootprints;
    private Boolean notStrata;
    private Boolean IncludeStrataPropertiesForMaster;
}
