package com.arealytics.core.dto.request;

import com.arealytics.core.enumeration.Prefix;

import lombok.*;

import com.arealytics.core.enumeration.Quadrant;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Positive;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = false)
public class AdditionalAddressRequestDTO {
    private String  addressStreetName;
    private Boolean  addressType;
    private String  buildingNumber;
    @Positive(message = "CityId must be a positive number")
    private Integer  cityId;
    @Positive(message = "CountryId must be a positive number")
    private Integer  countryId;
    @Positive(message = "CountyId must be a positive number")
    private Integer  countyId;
    private String  eastWestSt;
    private Boolean  isActive;
    private String  northSouthSt;
    private Prefix streetPrefix1;
    private Prefix streetPrefix2;
    private Integer  parentId;
    private Quadrant  quadrantId;
    private Integer  stateId;
    @Min(value = 1, message = "Street Number Min must be greater than 0")
    private Integer  streetNumberMin;
    @Min(value = 1, message = "Street Number Max must be greater than 0")
    private Integer  streetNumberMax;
    @Positive(message = "StreetSuffix1 must be a positive number")
    private Integer  streetSuffix1;
    @Positive(message = "StreetSuffix2 must be a positive number")
    private Integer  streetSuffix2;
    private Integer  zipCode;
}


