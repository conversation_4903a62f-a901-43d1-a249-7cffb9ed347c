package com.arealytics.core.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@RequiredArgsConstructor
public class BuildingFootPrintRequestDTO {
    @JsonProperty("BuildingFootPrintId")
    private Long buildingFootPrintId;

    @NotEmpty(message = "Building foot-print is required") @JsonProperty("BuildingFootPrint")
    private String buildingFootPrint;

    @Min(value = 0, message = "Area should not be negative") @JsonProperty("Area")
    private Double area;

    @JsonProperty("MinFloorNumber")
    @Min(value = 0, message = "MinFloorNumber should not be negative") private Integer minFloorNumber;

    @Min(value = 0, message = "MaxFloorNumber should not be negative") @JsonProperty("MaxFloorNumber")
    private Integer maxFloorNumber;

    @JsonProperty("UseTypeId")
    private Integer useTypeId;

    @JsonProperty("Description")
    private String description;

    @JsonProperty("AdditionalUseTypeId")
    private Integer additionalUseTypeId;

    @JsonProperty("AdditionalSpecificUseTypeId")
    private Integer additionalSpecificUseTypeId;

    @JsonProperty("MainSpecificUseTypeId")
    private Integer mainSpecificUseTypeId;
}
