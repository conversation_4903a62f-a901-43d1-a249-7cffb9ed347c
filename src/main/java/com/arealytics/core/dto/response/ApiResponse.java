package com.arealytics.core.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class ApiResponse<T> {
    private boolean error;
    private String message;
    private T responseData;
    private Integer status;

    public ApiResponse(boolean error, String message, T responseData, int status) {
        this.error = error;
        this.message = message;
        this.responseData = responseData;
        this.status = status;
    }
}
