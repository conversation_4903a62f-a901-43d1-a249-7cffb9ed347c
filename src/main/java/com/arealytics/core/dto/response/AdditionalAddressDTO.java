package com.arealytics.core.dto.response;

import lombok.*;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.time.Instant;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class AdditionalAddressDTO extends AddressDTO {
    private Integer parentId;
    private Integer locationId;
    private Boolean isActive;
    private String stateDisplayName;
    private String cityDisplayName;
    private String stateAbbr;
    private Integer createdBy;
    private Instant createdDate;
    private Integer modifiedBy;
    private Instant modifiedDate;
}
