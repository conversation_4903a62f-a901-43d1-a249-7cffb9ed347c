package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.*;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@Builder
public class PropertyDetailsResponseDTO extends BaseDTO {
    @JsonProperty("PropertyID")
    private Integer propertyID;

    @JsonProperty("PropertyName")
    private String propertyName;

    @JsonProperty("ConstructionStatusID")
    private ConstructionStatus constructionStatusID;

    @JsonProperty("ConstructionTypeID")
    private ConstructionType constructionTypeID;

    @JsonProperty("HVACTypeID")
    private HVACType hvacTypeID;

    @JsonProperty("SprinklerTypeID")
    private SprinklerType sprinklerTypeID;

    @JsonProperty("EstimatedCompletionDate")
    private LocalDate estimatedCompletionDate;

    @JsonProperty("IsADAAccessible")
    private Boolean isADAAccessible;

    @JsonProperty("IsVented")
    private Boolean isVented;

    @JsonProperty("IsOwnerOccupied")
    private Boolean isOwnerOccupied;

    @JsonProperty("TenancyTypeID")
    private Tenancy tenancyTypeID;

    @JsonProperty("IsEnergyStar")
    private Boolean isEnergyStar;

    @JsonProperty("NRASizeSourceID")
    private SizeSource NRASizeSourceID;

    @JsonProperty("BuildingComments")
    private String buildingComments;

    @JsonProperty("MetroId")
    private Integer metroId;

    @JsonProperty("MarketId")
    private Integer marketId;

    @JsonProperty("GovernmentInterestID")
    private GovernmentInterest governmentInterestID;

    @JsonProperty("ConstructionStartDate")
    private Date constructionStartDate;

    @JsonProperty("EstCompletion")
    private Date estCompletion;

    @JsonProperty("NoOfOfficeFloors")
    private Integer noOfOfficeFloors;

    @JsonProperty("PassengerElevators")
    private Integer passengerElevators;

    @JsonProperty("ParkingElevators")
    private Integer parkingElevators;

    @JsonProperty("FreighElevators")
    private Integer freighElevators;

    @JsonProperty("DockHigh")
    private Integer dockHigh;

    @JsonProperty("GradeLevelIn")
    private Integer gradeLevelIn;

    @JsonProperty("TruckWell")
    private Integer truckwell;

    @JsonProperty("PowerComments")
    private String powerComments;

    @JsonProperty("BayWidth")
    private BigDecimal bayWidth;

    @JsonProperty("BayDepth")
    private BigDecimal bayDepth;

    @JsonProperty("Depth")
    private BigDecimal depth;

    @JsonProperty("Width")
    private BigDecimal width;

    @JsonProperty("IncludeInAnalytics")
    private Boolean includeInAnalytics;

    @JsonProperty("GBASizeSource")
    private String GBASizeSource;

    @JsonProperty("OfficeAC")
    private HVACType officeAC;

    @JsonProperty("LotSizeSourceID")
    private SizeSource lotSizeSourceID;

    @JsonProperty("ZoningClassID")
    private ZoningClass zoningClassID;

    @JsonProperty("ZoningCode")
    private String zoningCode;

    @JsonProperty("PotentialZoningID")
    private ZoningClass potentialZoningID;

    @JsonProperty("SurroundingLandUse")
    private ZoningClass surroundingLandUse;

    @JsonProperty("RailServed")
    private Boolean railServed;

    @JsonProperty("IsFloodPlain")
    private Boolean isFloodPlain;

    @JsonProperty("smallestFloor")
    private Integer smallestFloor;

//    @JsonProperty("smallestFloorSM")
//    private Double smallestFloorSM;

    @JsonProperty("largestFloor")
    private Integer largestFloor;

//    @JsonProperty("largestFloorSM")
//    private Double largestFloorSM;

    @JsonProperty("RoofTypeID")
    private RoofType roofTypeID;

    @JsonProperty("CraneServed")
    private Boolean craneServed;

    @JsonProperty("HasYardFenced")
    private Boolean hasYardFenced;

    @JsonProperty("HasYardUnfenced")
    private Boolean hasYardUnfenced;

    @JsonProperty("BuildSpecStatusID")
    private BuildSpecStatus buildSpecStatusID;

    @JsonProperty("LegalDescription")
    private String legalDescription;

    @JsonProperty("CondoTypeID")
    private CondoType condoTypeID;

    @JsonProperty("InternalComments")
    private String internalComments;

    @JsonProperty("YearBuilt")
    private Integer yearBuilt;

    @JsonProperty("YearRenovated")
    private Integer yearRenovated;

    @JsonProperty("ParkingSpaces")
    private Integer parkingSpaces;

    @JsonProperty("HasSprinkler")
    private Boolean hasSprinkler;

    @JsonProperty("SizeSourceID")
    private SizeSource sizeSourceID;

    @JsonProperty("NoOfAnchor")
    private Integer noOfAnchor;

    @JsonProperty("HasPortAccess")
    private Boolean hasPortAccess;

    @JsonProperty("HasYard")
    private Boolean hasYard;

    @JsonProperty("HasResCoveredParking")
    private Boolean hasResCoveredParking;

    @JsonProperty("ParkingRatio")
    private String parkingRatio;

    @JsonProperty("EarthquakeZoneID")
    private Integer earthquakeZoneID;

    @JsonProperty("GeoscapePropertyID")
    private String geoscapePropertyID;

    @JsonProperty("CounsilTaxID")
    private String counsilTaxID;

    @JsonProperty("ValuerGeneralID")
    private String valuerGeneralID;

    @JsonProperty("ClearHeightMin")
    private BigDecimal clearHeightMin;

    @JsonProperty("ClearHeightMax")
    private BigDecimal clearHeightMax;

    @JsonProperty("Volts")
    private Integer volts;

    @JsonProperty("Phase")
    private Integer phase;

    @JsonProperty("Amps")
    private Integer amps;

    @JsonProperty("Address")
    private AddressDTO address;
}
