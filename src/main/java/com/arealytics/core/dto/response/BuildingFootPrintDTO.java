package com.arealytics.core.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = false)
public class BuildingFootPrintDTO {
    @JsonProperty("BuildingFootPrintId")
    private Long buildingFootPrintId;

    @JsonProperty("BuildingFootPrint")
    private String buildingFootPrint;

    @JsonProperty("CRE_PropertyId")
    private Integer crePropertyId;

    @JsonProperty("SizeInSF")
    private Double sizeInSF;

    @JsonProperty("SizeInSM")
    private Double sizeInSM;

    @Min(value = 0, message = "MinFloorNumber should not be negative") @JsonProperty("PropertyMinFloor")
    private Integer propertyMinFloor;

    @Min(value = 0, message = "MaxFloorNumber should not be negative") @JsonProperty("PropertyMaxFloor")
    private Integer propertyMaxFloor;

    @JsonProperty("Floors")
    private Integer floors;

    @JsonProperty("IsDefault")
    private Boolean isDefault;

    @JsonProperty("UseTypeId")
    private Integer useTypeId;

    @JsonProperty("AdditionalUseTypeId")
    private Integer additionalUseTypeId;

    @JsonProperty("MainSpecificUseTypeId")
    private Integer mainSpecificUseTypeId;

    @JsonProperty("AdditionalSpecificUseTypeId")
    private Integer additionalSpecificUseTypeId;

    @JsonProperty("Description")
    private String description;
}
