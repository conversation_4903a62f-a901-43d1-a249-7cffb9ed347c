package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.AddressType;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.enumeration.Quadrant;
import com.fasterxml.jackson.annotation.JsonProperty;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

import com.arealytics.core.enumeration.Prefix;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class AddressDTO {

    private Integer addressId;

    private AddressType addressTypeId;

    private String addressStreetNumber;

    private String addressStreetName;

    private String addressText;

    private String zipCode;

    private ParentTable parentTableId;

    private Integer sequence;

    private Integer streetSuffix1;

    private Integer streetSuffix2;

    private String zip4;

    private String floorNumber;

    @JsonProperty("CityID")
    private Integer cityId;

    @JsonProperty("StateID")
    private Integer stateId;

    @JsonProperty("CountyID")
    private Integer countyId;

    @JsonProperty("CountryID")
    private Integer countryId;

    private String address1;

    private String address2;

    private String streetNumberMin;

    private String streetNumberMax;

    @JsonProperty("EastWestStreet")
    private String eastWestSt;

    @JsonProperty("NorthSouthStreet")
    private String northSouthSt;

    @JsonProperty("Quadrant")
    private Quadrant quadrantID;

    private Prefix streetPrefix1;

    private Prefix streetPrefix2;

    private String buildingNumber;

    @JsonProperty("PartOfComplex")
    private Integer partOfCenterComplex;

    private String complexName;

    private String primaryStreet;

    private String primaryAccess;

    private String primaryTrafficCount;

    private LocalDateTime primaryTrafficCountDate;

    private String primaryFrontage;

    private String secondaryStreet;

    private String secondaryAccess;

    private String secondaryTrafficCount;

    @JsonProperty("SecTrafficCntDate")
    private LocalDateTime secondaryTrafficCountDate;

    private String secondaryFrontage;

    @JsonProperty("AddressType")
    private Boolean addressType;
}
