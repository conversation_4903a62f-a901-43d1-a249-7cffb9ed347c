package com.arealytics.core.dto.response;

import com.arealytics.core.enumeration.AddressType;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.enumeration.Quadrant;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AddressDTO {
    @JsonProperty("AddressID")
    private Integer addressId;

    @JsonProperty("AddressTypeID")
    private AddressType addressTypeId;

    @JsonProperty("ParentTableID")
    private ParentTable parentTableId;

    @JsonProperty("Sequence")
    private Integer sequence;

    @JsonProperty("StreetSuffix1")
    private Integer streetSuffix1;

    @JsonProperty("StreetSuffix2")
    private Integer streetSuffix2;

    @JsonProperty("Zip4")
    private String zip4;

    @JsonProperty("FloorNumber")
    private String floorNumber;

    @JsonProperty("CityID")
    private Integer cityId;

    @JsonProperty("StateID")
    private Integer stateId;

    @JsonProperty("CountyID")
    private Integer countyId;

    @JsonProperty("Address1")
    private String address1;

    @JsonProperty("Address2")
    private String address2;

    @JsonProperty("StreetNumberMin")
    private String streetNumberMin;

    @JsonProperty("StreetNumberMax")
    private String streetNumberMax;

    @JsonProperty("EastWestSt")
    private String eastWestSt;

    @JsonProperty("NorthSouthSt")
    private String northSouthSt;

    @JsonProperty("QuadrantID")
    private Quadrant quadrantID;

    @JsonProperty("streetPrefix1")
    private Integer streetPrefix1;

    @JsonProperty("streetPrefix2")
    private Integer streetPrefix2;

    @JsonProperty("BuildingNumber")
    private String buildingNumber;

    @JsonProperty("PartOfCenterComplex")
    private Integer partOfCenterComplex;

    @JsonProperty("ComplexName")
    private String complexName;

    @JsonProperty("PrimaryStreet")
    private String primaryStreet;

    @JsonProperty("PrimaryAccess")
    private String primaryAccess;

    @JsonProperty("PrimaryTrafficCount")
    private String primaryTrafficCount;

    @JsonProperty("PrimaryTrafficCountDate")
    private LocalDateTime primaryTrafficCountDate;

    @JsonProperty("PrimaryFrontage")
    private String primaryFrontage;

    @JsonProperty("SecondaryStreet")
    private String secondaryStreet;

    @JsonProperty("SecondaryAccess")
    private String secondaryAccess;

    @JsonProperty("SecondaryTrafficCount")
    private String secondaryTrafficCount;

    @JsonProperty("SecondaryTrafficCountDate")
    private LocalDateTime secondaryTrafficCountDate;

    @JsonProperty("SecondaryFrontage")
    private String secondaryFrontage;
}
