package com.arealytics.core.dto.response;
import com.arealytics.core.dto.response.PropertySearchDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.AllArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PropertySearchSizeResponseDTO extends PropertySearchDTO {

    @JsonProperty("LotSizeSM")
    private BigDecimal lotSizeSM;

    @JsonProperty("LotSizeSMFormatted")
    private String lotSizeSMFormatted;

    @JsonProperty("LotSizeACSM")
    private BigDecimal lotSizeACSM;

    @JsonProperty("BuildingSizeSM")
    private BigDecimal buildingSizeSM;

    @JsonProperty("BuildingSizeSMFormatted")
    private String buildingSizeSMFormatted;

    @JsonProperty("ContributedGBA_SM")
    private BigDecimal contributedGBASizeSM;

    @JsonProperty("ContributedGBA_SMFormatted")
    private String contributedGBASizeSMFormatted;
}
