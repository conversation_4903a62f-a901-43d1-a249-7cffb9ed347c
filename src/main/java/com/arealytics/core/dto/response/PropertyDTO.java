package com.arealytics.core.dto.response;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import com.arealytics.core.enumeration.*;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertyDTO {

    private Integer propertyID;

    private String propertyName;

    private Integer yearBuilt;

    private Integer floors;

    private CondoType condoTypeID;

    private String CondoUnit;

    private Integer useTypeID;

    private String useTypeName;

    private Integer specificUsesID;

    private String specificUseName;

    private String buildingComments;

    @JsonProperty("BuildingSF")
    private BigDecimal buildingSizeSF;

    @JsonProperty("BuildingSM")
    private BigDecimal buildingSizeSM;

    @JsonProperty("PropertyResearchTypeID")
    private Integer researchTypeID;

    private String researchTypeName;

    private Boolean isSkipped;

    private String mainPhotoURL;

    private String AuditStatus;

    private Integer lastReviewedBy;

    private String Address;

    private String City;

    private String State;

    private String County;

    private String Country;

    private String ZipCode;

    private BigDecimal Latitude;

    private BigDecimal Longitude;

    private String StreetNumberMin;

    private String StreetNumberMax;

    private String AddressStreetName;
}
