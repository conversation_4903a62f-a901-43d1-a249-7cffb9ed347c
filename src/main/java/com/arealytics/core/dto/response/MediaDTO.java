package com.arealytics.core.dto.response;

import java.math.BigDecimal;

import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.enumeration.MediaSource;
import com.arealytics.core.enumeration.MediaSubType;
import com.arealytics.core.enumeration.MediaType;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MediaDTO extends BaseDTO {
  @JsonProperty("MediaID")
  private Integer mediaId;

  @JsonProperty("MediaName")
  private String mediaName;

  @JsonProperty("Height")
  private Double height;

  @JsonProperty("Width")
  private Double width;

  @JsonProperty("Size")
  private Double size;

  @JsonProperty("Path")
  private String path;

  @JsonProperty("Ext")
  private String ext;

  @JsonProperty("Description")
  private String description;

  @JsonProperty("ModifiedByName")
  private String modifiedByName;

  @JsonProperty("MediaRelationshipID")
  private Integer mediaRelationshipId;

  @Enumerated(EnumType.STRING)
  @JsonProperty("MediaRelationTypeID")
  private MediaRelationType mediaRelationTypeId;

  @Enumerated(EnumType.STRING)
  @JsonProperty("MediaTypeID")
  private MediaType mediaTypeId;

  @Enumerated(EnumType.STRING)
  @JsonProperty("MediaSubTypeID")
  private MediaSubType mediaSubTypeId;

  @JsonProperty("RelationID")
  private Integer relationId;

  @JsonProperty("IsDefault")
  private Boolean isDefault;

  @JsonProperty("IsOwnMedia")
  private Boolean isOwnMedia;

  @Enumerated(EnumType.STRING)
  @JsonProperty("MediaSourceID")
  private MediaSource mediaSourceId;

  @JsonProperty("SourceComments")
  private String sourceComments;

  @JsonProperty("HasEdit")
  private Boolean hasEdit;

  @JsonProperty("BuildingSizeSF")
  private BigDecimal buildingSizeSF;
}
