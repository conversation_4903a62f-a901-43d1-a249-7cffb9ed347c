package com.arealytics.core.dto.response;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

import com.arealytics.core.enumeration.*;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PropertySearchDTO {

    private Integer propertyID;

    private String propertyName;

    private Boolean isActive;

    private Integer yearBuilt;

    private Integer floors;

    private CondoType condoTypeID;

    private String CondoUnit;

    private Integer useTypeID;

    private String useTypeName;

    private Integer specificUsesID;

    private String specificUseName;

    private String buildingComments;

    private Integer lastReviewedBy;

    private LocalDateTime lastReviewedDate;

    private BigDecimal buildingSize;

    @JsonProperty("LotSizeAcres")
    private BigDecimal lotSize;

    @JsonProperty("BuildingSF")
    private BigDecimal buildingSizeSF;

    @JsonProperty("LotSizeSF")
    private BigDecimal lotSizeSF;

    @JsonProperty("PropertyResearchTypeID")
    private Integer researchTypeID;

    private String researchTypeName;

    private Boolean isSkipped;

    private String mainPhotoURL;

    private Boolean hasNoBuildingFootprints;

    private Boolean hasNoExistingParcelInTileLayer;

    private Integer masterPropertyId;

    @JsonProperty("Address")
    private AddressDTO address;

    @JsonProperty("Location")
    private LocationDTO location;

    private Instant CreatedDate;

    private Instant ModifiedDate;

    private Integer CreatedBy;

    private Integer ModifiedBy;

    private String AuditStatus;

    private String CityName;

    private List<PropertyDTO> strataProperties;
}
