package com.arealytics.core.controller;

import java.util.List;

import com.arealytics.core.dto.querydsl.PropertyMapSearchQuerydslDTO;
import com.arealytics.core.dto.querydsl.PropertySearchQuerydslDTO;
import com.arealytics.core.dto.request.*;
import com.arealytics.core.dto.response.*;
import com.arealytics.core.enumeration.CondoType;

import com.arealytics.core.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.dto.querydsl.MasterPropertiesDTO;
import com.arealytics.core.enumeration.StatusCode;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Null;

@RestController
@RequestMapping("/property")
@Tag(name = "Property", description = "Operations related to property")
public class PropertyController {
    private final ParcelService parcelService;
    private final BuildingFootPrintService buildingFootPrintService;
    private final PropertyService propertyService;
    private final PropertyStrataService propertyStrataService;
    private final AddressService addressService;

    @Autowired
    public PropertyController(
            ParcelService parcelService,
            BuildingFootPrintService buildingFootPrintService,
            PropertyService propertyService, PropertyStrataService propertyStrataService, AddressService addressService) {
        this.parcelService = parcelService;
        this.buildingFootPrintService = buildingFootPrintService;
        this.propertyService = propertyService;
        this.propertyStrataService = propertyStrataService;
        this.addressService = addressService;
    }

    // Property parcel API's
    @Operation(
            summary = "Fetch Parcels by Property ID",
            description =
                    "Retrieves all parcel details associated with the specified property ID. If no"
                            + " parcels are found, an empty list is returned")
    @GetMapping("/{propertyId}/parcels")
    public ResponseEntity<ApiResponse<List<ParcelPropertyDTO>>> getParcelByProperty(
            @Parameter(description = "ID of the property to fetch parcels for") @PathVariable
                    Integer propertyId) {
        List<ParcelPropertyDTO> parcels = parcelService.getParcelByProperty(propertyId);
        ApiResponse<List<ParcelPropertyDTO>> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        parcels,
                        HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Create a Parcel",
            description = "This API creates a new Parcel if the ParcelID is not provided")
    @PostMapping("{propertyId}/parcels")
    public ResponseEntity<ApiResponse<ParcelPropertyDTO>> createParcel(
            @Valid @RequestBody ParcelPropertyRequestDTO parcelPropertyRequestDTO,
            @PathVariable Integer propertyId) {
        ParcelPropertyDTO parcelPropertyDTO =
                parcelService.createParcel(parcelPropertyRequestDTO, propertyId);
        ApiResponse<ParcelPropertyDTO> response =
                new ApiResponse<>(false, "Success", parcelPropertyDTO, HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Update a Parcel",
            description =
                    "This API updates an existing Parcel if the ParcelID and PropertyID is present"
                            + " in the request body.")
    @PutMapping("{propertyId}/parcels/{parcelId}")
    public ResponseEntity<ApiResponse<ParcelPropertyDTO>> updateParcel(
            @Valid @RequestBody ParcelPropertyRequestDTO parcelPropertyRequestDTO,
            @PathVariable Integer propertyId,
            @PathVariable Integer parcelId) {
        ParcelPropertyDTO parcelPropertyDTO =
                parcelService.updateParcel(parcelPropertyRequestDTO, parcelId, propertyId);
        ApiResponse<ParcelPropertyDTO> response =
                new ApiResponse<>(false, "Success", parcelPropertyDTO, HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Delete a Parcel",
            description =
                    "Deletes a specific parcel associated with the given property ID. This"
                            + " operation is irreversible. Make sure the ParcelID is correct before"
                            + " proceeding.")
    @DeleteMapping("{propertyId}/parcels/{parcelId}")
    public ResponseEntity<ApiResponse<Null>> deleteParcel(
            @PathVariable Integer propertyId, @PathVariable Integer parcelId) {
        parcelService.deleteParcel(parcelId, propertyId);
        ApiResponse<Null> response =
                new ApiResponse<>(false, "Success", null, HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }

    // Building Footprint API's
    @Operation(
            summary = "Fetch Building Footprints by Property ID",
            description =
                    "Retrieves all building footprint details associated with the specified"
                        + " property ID. Returns a list of building footprints for the given"
                        + " property ID. If no footprints are found, an empty list is returned.")
    @GetMapping("/{propertyId}/building-footprints")
    public ResponseEntity<ApiResponse<List<BuildingFootPrintDTO>>> getBuildingFootprintByPropertyId(
            @Parameter(description = "ID of the property to fetch building footprints for")
                    @PathVariable
                    Integer propertyId)
            throws Exception {
        List<BuildingFootPrintDTO> buildingFootprints =
                buildingFootPrintService.getBuildingFootprintsByPropertyId(propertyId);
        ApiResponse<List<BuildingFootPrintDTO>> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        buildingFootprints,
                        StatusCode.SUCCESS.getStatus().value());
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/building-footprints/delete")
    public ResponseEntity<ApiResponse<Void>> deleteBuildingFootprints(
            @Valid @RequestBody(required = true) BuildingFootPrintDeleteDTO request) {
        if (request.getPropertyId() == null
                || request.getBuildingFootPrintIDs() == null
                || request.getBuildingFootPrintIDs().isBlank()) {
            throw new IllegalArgumentException(
                    "Both propertyId and buildingFootPrintIDs are required.");
        }
        buildingFootPrintService.deleteBuildingFootprint(
                request.getBuildingFootPrintIDs(), request.getPropertyId());
        ApiResponse<Void> response =
                new ApiResponse<>(false, "Success", null, StatusCode.SUCCESS.getStatus().value());
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Save or Update Building Footprints",
            description =
                    "Creates new or updates existing building footprints associated with a"
                            + " property. If a `BuildingFootPrintId` is provided, the corresponding"
                            + " footprint will be updated. If not, a new footprint will be created."
                            + " Returns the updated list of footprints for the given property ID.")
    @PostMapping("/building-footprints")
    public ResponseEntity<ApiResponse<List<BuildingFootPrintDTO>>> saveBuildingFootprints(
            @Valid @RequestBody BuildingFootprintCollectionRequestDTO request) throws Exception {
        List<BuildingFootPrintDTO> buildingFootprints =
                buildingFootPrintService.saveBuildingFootprints(request);
        ApiResponse<List<BuildingFootPrintDTO>> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        buildingFootprints,
                        StatusCode.SUCCESS.getStatus().value());
        return ResponseEntity.ok(response);
    }

    // property create and update apis

    @Operation(
            summary = "Create a new Property",
            description =
                    "Creates a new property using the provided property data. Returns the created"
                            + " property details.")
    @PostMapping("")
    public ResponseEntity<ApiResponse<PropertyResponseDTO>> createProperty(
            @RequestBody PropertyDetailsDTO propertyDTO) {

        PropertyResponseDTO savedProperty = propertyService.createProperty(propertyDTO);

        ApiResponse<PropertyResponseDTO> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        savedProperty,
                        StatusCode.SUCCESS.getStatus().value());

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(
            summary = "Update an existing Property",
            description =
                    "Updates the property details for the given property. The property must exist;"
                            + " otherwise, an error will be returned. Returns the updated property"
                            + " details.")
    @PutMapping("")
    public ResponseEntity<ApiResponse<PropertyResponseDTO>> updateProperty(
            @RequestBody PropertyDetailsDTO propertyDTO) {

        PropertyResponseDTO updatedProperty = propertyService.updateProperty(propertyDTO);

        ApiResponse<PropertyResponseDTO> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        updatedProperty,
                        StatusCode.SUCCESS.getStatus().value());

        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Get Property Details",
            description = "Retrieves detailed information about a property based on its ID")
    @GetMapping("/{propertyId}/details")
    public ResponseEntity<ApiResponse<PropertyDetailsSizeResponseDTO>> findPropertyDetailsByPropertyID(
            @Parameter(description = "PropertyId to fetch details for")
            @PathVariable Integer propertyId) {

        PropertyDetailsSizeResponseDTO responseDto = propertyService.findPropertyDetailsByPropertyID(propertyId);

        ApiResponse<PropertyDetailsSizeResponseDTO> response = new ApiResponse<>(
                ApiResponseConstants.IS_ERROR, // Typically false
                ApiResponseConstants.SUCCESS_MESSAGE, // e.g., "Success"
                responseDto,
                StatusCode.SUCCESS.getStatus().value() // e.g., 200
        );

        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Link child pids to master",
            description =
                    "Establishes a relationship between a master property and one or more child"
                        + " properties. Validates property types before linking and ensures"
                        + " existing relationships are deactivated if the child property is already"
                        + " linked to a different master. Only active, valid relationships are"
                        + " returned.")
    @PostMapping("/link-child-to-master")
    public ResponseEntity<ApiResponse<List<PropertyStrataRelationshipResponseDTO>>>
            linkChildToMaster(@RequestBody PropertyStrataRelationshipRequestDTO request)
                    throws Exception {
        List<PropertyStrataRelationshipResponseDTO> propertyData =
                propertyStrataService.linkChildToMaster(request);
        ApiResponse<List<PropertyStrataRelationshipResponseDTO>> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        propertyData,
                        StatusCode.SUCCESS.getStatus().value());
                return ResponseEntity.ok(response);
        }

    @Operation(summary = "Get strata details for a property", description = "Fetches strata-related details for a given property ID, which can be either a master or a child property. "
        + "If the provided property is a child, the associated master property and all its related strata properties "
        + "are returned. Validates the property's condo type and ensures only active relationships are considered.")
    @GetMapping("/get-linked-properties/{propertyId}")
    public ResponseEntity<ApiResponse<List<PropertyStrataDetailsDTO>>> getLinkedPropertyDetails(
        @PathVariable Integer propertyId) {
      List<PropertyStrataDetailsDTO> propertyStrataDetails = propertyStrataService.getLinkedPropertyDetails(propertyId);
      ApiResponse<List<PropertyStrataDetailsDTO>> response = new ApiResponse<>(
          ApiResponseConstants.IS_ERROR,
          ApiResponseConstants.SUCCESS_MESSAGE,
          propertyStrataDetails,
          StatusCode.SUCCESS.getStatus().value());
      return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Get master properties by search",
            description = "Fetch master properties for a given search text and strata type")
    @GetMapping("/get-master-properties/{searchText}/{strataType}")
    public ResponseEntity<ApiResponse<List<MasterPropertiesDTO>>> masterPropertiesSearch(
        @PathVariable String searchText, @PathVariable CondoType strataType) {
      List<MasterPropertiesDTO> masterProperties = propertyStrataService.getMasterProperties(searchText, strataType);
      ApiResponse<List<MasterPropertiesDTO>> response = new ApiResponse<>(
          ApiResponseConstants.IS_ERROR,
          ApiResponseConstants.SUCCESS_MESSAGE,
          masterProperties,
          StatusCode.SUCCESS.getStatus().value());
      return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Get properties by search",
            description = "Fetches properties for a given filters")
    @PostMapping("/search")
    public ResponseEntity<ApiResponse<List<PropertySearchSizeResponseDTO>>> getPropertiesBySearch(
            @RequestBody PropertySearchRequestDTO searchCriteria) {

        List<PropertySearchSizeResponseDTO> properties = propertyService.findPropertiesBySearch(searchCriteria);

        Long propertiesCount = propertyService.findCountPropertiesBySearch(searchCriteria);

        ApiResponse<List<PropertySearchSizeResponseDTO>> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        properties,
                        StatusCode.SUCCESS.getStatus().value(),
                        propertiesCount);
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Get properties by search",
            description = "Fetches properties for a given filters")
    @PostMapping("/mapsearch")
    public ResponseEntity<ApiResponse<List<PropertyMapSearchSizeResponseDTO>>> getPropertiesByMapSearch(
            @RequestBody PropertyMapSearchRequestDTO searchCriteria) {

        List<PropertyMapSearchSizeResponseDTO> properties = propertyService.findPropertiesByMapSearch(searchCriteria);

        Long propertiesCount = propertyService.findCountPropertiesByMapSearch(searchCriteria);

        ApiResponse<List<PropertyMapSearchSizeResponseDTO>> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        properties,
                        StatusCode.SUCCESS.getStatus().value(),
                        propertiesCount);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get Additional Addresses for a Property", description = "Retrieves a list of additional address records associated with a given property ID. "
                    + "Excludes the primary (sequence 1) address and returns only active additional addresses.")
    @GetMapping("/{propertyId}/additional-address")
    public ResponseEntity<ApiResponse<List<AdditionalAddressDTO>>> getAdditionalAddress(
                    @PathVariable Integer propertyId) {
            List<AdditionalAddressDTO> additionalAddressDetails = addressService.getAdditionalAddress(propertyId);
            ApiResponse<List<AdditionalAddressDTO>> response = new ApiResponse<>(
                            ApiResponseConstants.IS_ERROR,
                            ApiResponseConstants.SUCCESS_MESSAGE,
                            additionalAddressDetails,
                            StatusCode.SUCCESS.getStatus().value());
            return ResponseEntity.ok(response);
    }

    @Operation(summary = "Save a New Additional Address", description = "Creates a new additional address for the specified property. "
                    + "The sequence number is auto-generated, and inherited fields like countryId and locationId "
                    + "are copied from the property's primary address if available. Returns the updated list of additional addresses.")
    @PostMapping("/additional-address")
    public ResponseEntity<ApiResponse<AdditionalAddressDTO>> saveAdditionalAddress(
                  @Valid @RequestBody AdditionalAddressRequestDTO request) {
            AdditionalAddressDTO additionalAddressDetails = addressService.saveAdditionalAddress(request);
            ApiResponse<AdditionalAddressDTO> response = new ApiResponse<>(
                            ApiResponseConstants.IS_ERROR,
                            ApiResponseConstants.SUCCESS_MESSAGE,
                            additionalAddressDetails,
                            StatusCode.SUCCESS.getStatus().value());
            return ResponseEntity.ok(response);
    }

    @Operation(summary = "Update an Existing Additional Address", description = "Updates the details of an existing additional address using the provided address ID. "
                    + "Also regenerates the formatted address text and updates audit fields such as modified date and user.")
    @PutMapping("/additional-address/{additionalAddressId}")
    public ResponseEntity<ApiResponse<AdditionalAddressDTO>> updateAdditionalAddress(@PathVariable Integer additionalAddressId,
                  @Valid @RequestBody AdditionalAddressRequestDTO request) {
            AdditionalAddressDTO additionalAddressDetails = addressService.updateAdditionalAddress(additionalAddressId, request);
            ApiResponse<AdditionalAddressDTO> response = new ApiResponse<>(
                            ApiResponseConstants.IS_ERROR,
                            ApiResponseConstants.SUCCESS_MESSAGE,
                            additionalAddressDetails,
                            StatusCode.SUCCESS.getStatus().value());
            return ResponseEntity.ok(response);
    }

    @Operation(summary = "Delete an Additional Address", description = "Performs a soft delete of the specified additional address by setting its active status to false. "
                    + "Also updates the modified date and modified by fields for audit purposes.")
    @DeleteMapping("/additional-address/{additionalAddressId}")
    public ResponseEntity<ApiResponse<Null>> deleteAdditionalAddress(@PathVariable Integer additionalAddressId) {
            addressService.deleteAdditionalAddress(additionalAddressId);
            ApiResponse<Null> response = new ApiResponse<>(
                            ApiResponseConstants.IS_ERROR,
                            ApiResponseConstants.SUCCESS_MESSAGE,
                            null,
                            StatusCode.SUCCESS.getStatus().value());
            return ResponseEntity.ok(response);
    }

}
