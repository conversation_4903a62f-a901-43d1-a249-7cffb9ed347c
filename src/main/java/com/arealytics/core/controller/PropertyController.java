package com.arealytics.core.controller;

import java.util.List;

import com.arealytics.core.dto.response.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.dto.request.BuildingFootPrintDeleteDTO;
import com.arealytics.core.dto.request.BuildingFootprintCollectionRequestDTO;
import com.arealytics.core.dto.request.ParcelPropertyRequestDTO;
import com.arealytics.core.dto.request.PropertyDTO;
import com.arealytics.core.enumeration.StatusCode;
import com.arealytics.core.service.BuildingFootPrintService;
import com.arealytics.core.service.ParcelService;
import com.arealytics.core.service.PropertyService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Null;

@RestController
@RequestMapping("/property")
@Tag(name = "Property", description = "Operations related to property")
public class PropertyController {
    private final ParcelService parcelService;
    private final BuildingFootPrintService buildingFootPrintService;
    private final PropertyService propertyService;

    @Autowired
    public PropertyController(
            ParcelService parcelService,
            BuildingFootPrintService buildingFootPrintService,
            PropertyService propertyService) {
        this.parcelService = parcelService;
        this.buildingFootPrintService = buildingFootPrintService;
        this.propertyService = propertyService;
    }

    // Property parcel API's
    @Operation(
            summary = "Fetch Parcels by Property ID",
            description =
                    "Retrieves all parcel details associated with the specified property ID. If no"
                            + " parcels are found, an empty list is returned")
    @GetMapping("/{propertyId}/parcels")
    public ResponseEntity<ApiResponse<List<ParcelPropertyDTO>>> getParcelByProperty(
            @Parameter(description = "ID of the property to fetch parcels for") @PathVariable
                    Integer propertyId) {
        List<ParcelPropertyDTO> parcels = parcelService.getParcelByProperty(propertyId);
        ApiResponse<List<ParcelPropertyDTO>> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        parcels,
                        HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Create a Parcel",
            description = "This API creates a new Parcel if the ParcelID is not provided")
    @PostMapping("{propertyId}/parcels")
    public ResponseEntity<ApiResponse<ParcelPropertyDTO>> createParcel(
            @Valid @RequestBody ParcelPropertyRequestDTO parcelPropertyRequestDTO,
            @PathVariable Integer propertyId) {
        ParcelPropertyDTO parcelPropertyDTO =
                parcelService.createParcel(parcelPropertyRequestDTO, propertyId);
        ApiResponse<ParcelPropertyDTO> response =
                new ApiResponse<>(false, "Success", parcelPropertyDTO, HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Update a Parcel",
            description =
                    "This API updates an existing Parcel if the ParcelID and PropertyID is present"
                            + " in the request body.")
    @PutMapping("{propertyId}/parcels/{parcelId}")
    public ResponseEntity<ApiResponse<ParcelPropertyDTO>> updateParcel(
            @Valid @RequestBody ParcelPropertyRequestDTO parcelPropertyRequestDTO,
            @PathVariable Integer propertyId,
            @PathVariable Integer parcelId) {
        ParcelPropertyDTO parcelPropertyDTO =
                parcelService.updateParcel(parcelPropertyRequestDTO, parcelId, propertyId);
        ApiResponse<ParcelPropertyDTO> response =
                new ApiResponse<>(false, "Success", parcelPropertyDTO, HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Delete a Parcel",
            description =
                    "Deletes a specific parcel associated with the given property ID. This"
                            + " operation is irreversible. Make sure the ParcelID is correct before"
                            + " proceeding.")
    @DeleteMapping("{propertyId}/parcels/{parcelId}")
    public ResponseEntity<ApiResponse<Null>> deleteParcel(
            @PathVariable Integer propertyId, @PathVariable Integer parcelId) {
        parcelService.deleteParcel(parcelId, propertyId);
        ApiResponse<Null> response =
                new ApiResponse<>(false, "Success", null, HttpStatus.OK.value());
        return ResponseEntity.ok(response);
    }

    // Building Footprint API's
    @Operation(
            summary = "Fetch Building Footprints by Property ID",
            description =
                    "Retrieves all building footprint details associated with the specified"
                        + " property ID. Returns a list of building footprints for the given"
                        + " property ID. If no footprints are found, an empty list is returned.")
    @GetMapping("/{propertyId}/building-footprints")
    public ResponseEntity<ApiResponse<List<BuildingFootPrintDTO>>> getBuildingFootprintByPropertyId(
            @Parameter(description = "ID of the property to fetch building footprints for")
                    @PathVariable
                    Integer propertyId)
            throws Exception {
        List<BuildingFootPrintDTO> buildingFootprints =
                buildingFootPrintService.getBuildingFootprintsByPropertyId(propertyId);
        ApiResponse<List<BuildingFootPrintDTO>> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        buildingFootprints,
                        StatusCode.SUCCESS.getStatus().value());
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/building-footprints/delete")
    public ResponseEntity<ApiResponse<Void>> deleteBuildingFootprints(
            @Valid @RequestBody(required = true) BuildingFootPrintDeleteDTO request) {
        if (request.getPropertyId() == null
                || request.getBuildingFootPrintIDs() == null
                || request.getBuildingFootPrintIDs().isBlank()) {
            throw new IllegalArgumentException(
                    "Both propertyId and buildingFootPrintIDs are required.");
        }
        buildingFootPrintService.deleteBuildingFootprint(
                request.getBuildingFootPrintIDs(), request.getPropertyId());
        ApiResponse<Void> response =
                new ApiResponse<>(false, "Success", null, StatusCode.SUCCESS.getStatus().value());
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Save or Update Building Footprints",
            description =
                    "Creates new or updates existing building footprints associated with a"
                            + " property. If a `BuildingFootPrintId` is provided, the corresponding"
                            + " footprint will be updated. If not, a new footprint will be created."
                            + " Returns the updated list of footprints for the given property ID.")
    @PostMapping("/building-footprints")
    public ResponseEntity<ApiResponse<List<BuildingFootPrintDTO>>> saveBuildingFootprints(
            @Valid @RequestBody BuildingFootprintCollectionRequestDTO request) throws Exception {
        List<BuildingFootPrintDTO> buildingFootprints =
                buildingFootPrintService.saveBuildingFootprints(request);
        ApiResponse<List<BuildingFootPrintDTO>> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        buildingFootprints,
                        StatusCode.SUCCESS.getStatus().value());
        return ResponseEntity.ok(response);
    }

    // property create and update apis

    @Operation(
            summary = "Create a new Property",
            description =
                    "Creates a new property using the provided property data. Returns the created"
                            + " property details.")
    @PostMapping("")
    public ResponseEntity<ApiResponse<PropertyResponseDTO>> createProperty(
            @RequestBody PropertyDTO propertyDTO) {

        PropertyResponseDTO savedProperty = propertyService.createProperty(propertyDTO);

        ApiResponse<PropertyResponseDTO> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        savedProperty,
                        StatusCode.SUCCESS.getStatus().value());

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(
            summary = "Update an existing Property",
            description =
                    "Updates the property details for the given property. The property must exist;"
                            + " otherwise, an error will be returned. Returns the updated property"
                            + " details.")
    @PutMapping("")
    public ResponseEntity<ApiResponse<PropertyResponseDTO>> updateProperty(
            @RequestBody PropertyDTO propertyDTO) {

        PropertyResponseDTO updatedProperty = propertyService.updateProperty(propertyDTO);

        ApiResponse<PropertyResponseDTO> response =
                new ApiResponse<>(
                        ApiResponseConstants.IS_ERROR,
                        ApiResponseConstants.SUCCESS_MESSAGE,
                        updatedProperty,
                        StatusCode.SUCCESS.getStatus().value());

        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Get Property Details",
            description = "Retrieves detailed information about a property based on its ID")
    @GetMapping("/{propertyId}/details")
    public ResponseEntity<?> findPropertyDetailsByPropertyID(
            @Parameter(description = "PropertyId to fetch details for") @PathVariable
            Integer propertyId) {
        PropertyDetailsResponseDTO response = propertyService.findPropertyDetailsByPropertyID(propertyId);
        return ResponseEntity.ok(response);
    }
}
