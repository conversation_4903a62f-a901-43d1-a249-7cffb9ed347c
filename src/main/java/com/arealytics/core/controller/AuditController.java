package com.arealytics.core.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.arealytics.core.constants.ApiResponseConstants;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.service.AuditService;

@RestController
@RequestMapping("/auditlogs")
public class AuditController {

    @Autowired
    private AuditService auditService;

    @GetMapping("/{entityType}/{entityId}")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getParcelAuditLogByParcelId(
            @PathVariable String entityType, @PathVariable Integer entityId) {
        List<Map<String, Object>> changelog = auditService.fetchAuditLogsByEntityId(entityType, entityId);

        ApiResponse<List<Map<String, Object>>> response = new ApiResponse<>(
                ApiResponseConstants.IS_ERROR,
                ApiResponseConstants.SUCCESS_MESSAGE,
                changelog,
                HttpStatus.OK.value());

        return ResponseEntity.ok(response);
    }
}
