package com.arealytics.core.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.dto.response.MediaDTO;
import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.service.MediaService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/media")
@Tag(name = "Media", description = "Operations related to media")
public class MediaController {
  private final MediaService mediaService;

  @Autowired
  public MediaController(MediaService mediaService) {
    this.mediaService = mediaService;
  }

  @Operation(summary = "Fetch Media by Relation ID", description = "This API retrieves media records based on the given `mediaRelationId` and"
      + " `relationId`. It supports fetching media for various types of relations"
      + " such as entities, properties, etc. The response includes additional"
      + " information about the media, such as edit permissions and building size"
      + " for property-related media.")
  @GetMapping("/{mediaRelationshipId}/{relationId}")
  public ResponseEntity<ApiResponse<List<MediaDTO>>> getMediaByRelationId(
      @PathVariable MediaRelationType mediaRelationshipId, @PathVariable Integer relationId) {
    List<MediaDTO> mediaDTO = mediaService.getMediaByRelationId(mediaRelationshipId, relationId);
    ApiResponse<List<MediaDTO>> response = new ApiResponse<>(false, "Success", mediaDTO, HttpStatus.OK.value());
    return ResponseEntity.ok(response);
  }
}
