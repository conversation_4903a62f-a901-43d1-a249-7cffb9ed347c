package com.arealytics.core.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.service.LookupService;

@RestController
@RequestMapping("/lookup")
public class LookupController {
    private final LookupService lookupService;

    @Autowired
    public LookupController(LookupService lookupService) {
        this.lookupService = lookupService;
    }

    @GetMapping("/property")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAllLookups() {
        Map<String, Object> lookups = lookupService.getAllLookups();
        ApiResponse<Map<String, Object>> response = new ApiResponse<>(false, "Success", lookups, HttpStatus.OK.value());
        return ResponseEntity.ok(response);
}
}
