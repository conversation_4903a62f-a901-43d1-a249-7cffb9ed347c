package com.arealytics.core.domain.empiricalGIS;

import java.time.Instant;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import lombok.Data;

@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class ALGISBaseEntity {

    @Column(name = "CreatedBy")
    private Integer CreatedBy;

    @CreatedDate
    @Column(name = "CreatedDate", updatable = false)
    private Instant createdDate;

    @Column(name = "ModifiedBy")
    private Integer modifiedBy;

    @LastModifiedDate
    @Column(name = "ModifiedDate")
    private Instant modifiedDate;
}
