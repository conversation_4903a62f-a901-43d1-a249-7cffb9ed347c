package com.arealytics.core.domain.empiricalProd;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import com.arealytics.core.converter.*;
import com.arealytics.core.enumeration.*;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PropertyDetails {

    @Column(name = "YearBuilt")
    private Integer yearBuilt;

    @Column(name = "YearRenovated")
    private Integer yearRenovated;

    @Column(name = "Floors")
    private Integer floors;

    @Convert(converter = ConstructionStatusConverter.class)
    @Column(name = "ConstructionStatusID")
    private ConstructionStatus constructionStatusID;

    @Convert(converter = ConstructionTypeConverter.class)
    @Column(name = "ConstructionTypeID")
    private ConstructionType constructionTypeID;

    @Convert(converter = HVACTypeConverter.class)
    @Column(name = "HVACTypeID")
    private HVACType hvacTypeID;

    @Convert(converter = OfficeHVACConvertor.class)
    @Column(name = "OfficeHVAC")
    private OfficeHvac officeHVAC;

    @Convert(converter = SprinklerTypeConverter.class)
    @Column(name = "SprinklerTypeID")
    private SprinklerType sprinklerTypeID;

    @Convert(converter = CondoTypeConverter.class)
    @Column(name = "CondoTypeID")
    private CondoType condoTypeID;

    @Column(name = "CondoUnit", length = 45)
    private String condoUnit;

    @Column(name = "UseTypeID")
    private Integer useTypeID;

    @Column(name = "UseTypeName", length = 25)
    private String useTypeName;

    @Column(name = "SpecificUseID")
    private Integer specificUseID;

    @Column(name = "SpecificUseName", length = 50)
    private String specificUseName;

    @Column(name = "IsADAAccessible")
    private Boolean isADAAccessible;

    @Column(name = "IsVented")
    private Boolean isVented;

    @Column(name = "IsOwnerOccupied")
    private Boolean isOwnerOccupied;

    @Convert(converter = TenancyConverter.class)
    @Column(name = "TenancyTypeID")
    private Tenancy tenancyTypeID;

    @Column(name = "IsEnergyStar")
    private Boolean isEnergyStar;

    @Convert(converter = ClassTypeConverter.class)
    @Column(name = "ClassTypeID")
    private ClassType classTypeID;

    @Column(name = "GroupID")
    private Integer groupID;

    @Column(name = "MixedUseAllocation", length = 100)
    private String mixedUseAllocation;

    @Column(name = "BuildingWebsite", length = 255)
    private String buildingWebsite;

    @Column(name = "BuildingComments", columnDefinition = "TEXT")
    private String buildingComments;

    @Convert(converter = GovernmentInterestConverter.class)
    @Column(name = "GovernmentInterestID")
    private GovernmentInterest governmentInterestID;

    @Column(name = "NoOfOfficeFloors")
    private Integer noOfOfficeFloors;

    @Column(name = "CraneServed")
    private Boolean craneServed;

    @Convert(converter = RoofTypeConverter.class)
    @Column(name = "RoofTypeID")
    private RoofType roofTypeID;

    @Column(name = "HasSolar")
    private Boolean hasSolar;

    @Column(name = "TrafficCount")
    private Integer trafficCount;

    @Column(name = "EarthquakeZoneID")
    private Integer earthquakeZoneID;

    @Column(name = "DeletedAt")
    private Date deletedAt;

    @Convert(converter = EnergyStarRatingConverter.class)
    @Column(name = "EnergyStarRatingID")
    private EnergyStarRating energyStarRatingID;

    @Convert(converter = WaterStarRatingConverter.class)
    @Column(name = "WaterStarRatingID")
    private WaterStarRating waterStarRatingID;

    @Convert(converter = GreenStarRatingConverter.class)
    @Column(name = "GreenStarRatingID")
    private GreenStarRating greenStarRatingID;

    @Column(name = "OccupiedPercentage", precision = 5, scale = 2)
    private BigDecimal occupiedPercentage;

    @Column(name = "DirectOccupiedPercentage", precision = 5, scale = 2)
    private BigDecimal directOccupiedPercentage;

    @Column(name = "CurrentTitle", length = 255)
    private String currentTitle;

    @Column(name = "TIAllowance", precision = 10, scale = 2)
    private BigDecimal tiAllowance;

    @Column(name = "GRESBScore", precision = 10, scale = 2)
    private BigDecimal gresbScore;

    @Column(name = "GRESBScoreMin")
    private Integer gresbScoreMin;

    @Column(name = "GRESBScoreMax")
    private Integer gresbScoreMax;

    @Column(name = "ActualCompletion")
    private LocalDateTime actualCompletion;

    @Column(name = "TitleReferenceDate")
    private LocalDateTime titleReferenceDate;

    @Column(name = "LandUse")
    private Integer landUse;

    @Column(name = "LastReviewedBy")
    private Integer lastReviewedBy;

    @Column(name = "LastReviewedDate")
    private LocalDateTime lastReviewedDate;

    @Column(name = "ConstructionStartDate")
    private LocalDateTime constructionStartDate;

    @Column(name = "EstCompletion")
    private LocalDateTime estCompletion;

    @Column(name = "EstimatedCompletionDate")
    private LocalDate estimatedCompletionDate;

    @Column(name = "BookValue")
    private Integer bookValue;

    @Column(name = "BookValueDate")
    private LocalDateTime bookValueDate;

    @Column(name = "MinAskingSalePrice", precision = 14, scale = 3)
    private BigDecimal minAskingSalePrice;

    @Column(name = "MaxAskingSalePrice", precision = 14, scale = 3)
    private BigDecimal maxAskingSalePrice;

    @Column(name = "AskingLeaseRatePerYearMin", precision = 14, scale = 3)
    private BigDecimal askingLeaseRatePerYearMin;

    @Column(name = "AskingLeaseRatePerYearMax", precision = 14, scale = 3)
    private BigDecimal askingLeaseRatePerYearMax;

    @Lob
    @Column(name = "ParcelInfo", columnDefinition = "TEXT")
    private String parcelInfo;

    @Column(name = "PropertyKey", length = 36)
    private String propertyKey;

    @Column(name = "MainPhotoUrl", length = 255)
    private String mainPhotoUrl;

    @Lob
    @Column(name = "ListingCompany", columnDefinition = "TEXT")
    private String listingCompany;

    @Column(name = "HasNoBuildingFootprints", columnDefinition="TINYINT")
    private Boolean hasNoBuildingFootprints;

    @Column(name = "HasNoExistingParcelInTileLayer", columnDefinition="TINYINT")
    private Boolean hasNoExistingParcelInTileLayer;

    @Column(name = "ContributedSourceComments", columnDefinition = "TEXT")
    private String contributedSourceComments;
}
