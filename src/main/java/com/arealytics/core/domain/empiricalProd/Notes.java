package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "Notes")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Notes extends ALTransactionBaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "NoteID", nullable = false, updatable = false)
    private Integer noteId;

    @Column(name = "NoteTypeID")
    private Integer noteTypeId;

    @Column(name = "NoteTitle", length = 100)
    private String noteTitle;

    @Lob
    @Column(name = "NoteDescription", columnDefinition = "MEDIUMTEXT")
    private String noteDescription;

    @Column(name = "ParentTableID")
    private Integer parentTableId;

    @Column(name = "ParentID")
    private Integer parentId;

    @Column(name = "IsActive")
    private Boolean isActive;
}
