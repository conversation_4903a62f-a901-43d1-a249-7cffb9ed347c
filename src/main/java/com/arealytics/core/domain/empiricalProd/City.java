package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.*;
import lombok.*;

import java.util.List;

@Entity
@Table(name = "City")
@Getter
@Setter
@NoArgsConstructor
public class City extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CityID")
    private Integer cityId;

    @Column(name = "CityName", length = 100, nullable = false)
    private String cityName;

    @Column(name = "StateID", nullable = false)
    private Integer stateId;

    @Column(name = "IsActive", nullable = false)
    private Boolean isActive;

    @Column(name = "ActiveMunicipality")
    private Boolean activeMunicipality;

    @Column(name = "ShowInPublic")
    private Boolean showInPublic;

    @Column(name = "DisplayOrderPublic")
    private Integer displayOrderPublic;

    @Column(name = "ImageUrl", length = 255)
    private String imageUrl;

    @Column(name = "DisplayName", length = 100)
    private String displayName;

    @Column(name = "MarketStateID")
    private Integer marketStateId;

    @OneToMany(mappedBy = "city", fetch = FetchType.LAZY)
    private List<Address> addresses;
}
