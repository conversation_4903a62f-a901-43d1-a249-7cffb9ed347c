package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.AddressTypeConverter;
import com.arealytics.core.converter.MediaRelationTypeConverter;
import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.converter.MediaRelationshipTypeConverter;
import com.arealytics.core.converter.MediaSubTypeConverter;
import com.arealytics.core.converter.MediaTypeConverter;
import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.enumeration.MediaSubType;
import com.arealytics.core.enumeration.MediaType;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "MediaRelationship")
@Getter
@Setter
@NoArgsConstructor
public class MediaRelationship {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "MediaRelationshipID")
  private Integer mediaRelationshipId;

  @OneToOne
  @JoinColumn(name = "MediaID", nullable = false)
  private Media media;

    @Convert(converter = MediaRelationTypeConverter.class)
    @Column(name = "MediaRelationTypeID")
    private MediaRelationType mediaRelationTypeId;

    @Convert(converter = MediaRelationshipTypeConverter.class)
    @Column(name = "MediaRelationTypeID")
    private MediaRelationType mediaRelationTypeId;

  @Convert(converter = MediaTypeConverter.class)
  @Column(name = "MediaTypeID")
  private MediaType mediaTypeId;

  @Convert(converter = MediaSubTypeConverter.class)
  @Column(name = "MediaSubTypeID")
  private MediaSubType mediaSubTypeId;

  @Column(name = "RelationID")
  private Integer relationId;

  @ManyToOne
  @JoinColumn(name = "PropertyID")
  private Property property;

  @Column(name = "IsDefault")
  private Boolean isDefault;

  @Column(name = "IsActive")
  private Boolean isActive;
}
