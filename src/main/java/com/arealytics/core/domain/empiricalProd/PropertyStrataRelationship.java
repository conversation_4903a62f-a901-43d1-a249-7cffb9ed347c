package com.arealytics.core.domain.empiricalProd;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "PropertyStrataRelationship")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PropertyStrataRelationship extends ALTransactionBaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "StrataRelationshipID")
  private Integer strataRelationshipId;

  @ManyToOne
  @JoinColumn(name = "MasterPropertyID")
  private Property masterProperty;

  @ManyToOne
  @JoinColumn(name = "StrataPropertyID")
  private Property strataProperty;

  @Column(name = "IsActive")
  private Boolean isActive = true;
}
