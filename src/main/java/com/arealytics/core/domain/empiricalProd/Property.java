package com.arealytics.core.domain.empiricalProd;

import com.fasterxml.jackson.annotation.JsonUnwrapped;

import jakarta.persistence.*;
import lombok.*;

import java.util.List;

import org.hibernate.envers.Audited;

@Entity
@Table(name = "Property")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Audited(withModifiedFlag = true)
public class Property extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PropertyID")
    private Integer propertyID;

    @Column(name = "PropertyName", length = 255, nullable = false)
    private String propertyName;

    @Column(name = "ParentPropertyID")
    private Integer parentPropertyID;

    @Column(name = "IsActive")
    private Boolean isActive;

    @Embedded @JsonUnwrapped private PropertyDetails propertyDetails;

    @Embedded @JsonUnwrapped private PropertyLocation location;

    @Embedded @JsonUnwrapped private PropertyAmenities amenities;

    @Embedded @JsonUnwrapped private PropertySize propertySize;

    @Embedded @JsonUnwrapped private PropertyInternalToolFields internalToolFields;

    @OneToMany(mappedBy = "property", fetch = FetchType.LAZY)
    private List<Address> addresses;

    @OneToMany(mappedBy = "property", fetch = FetchType.LAZY)
    private List<Use> genuses;
}
