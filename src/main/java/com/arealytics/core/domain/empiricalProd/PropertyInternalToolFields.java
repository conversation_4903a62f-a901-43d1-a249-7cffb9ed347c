package com.arealytics.core.domain.empiricalProd;

import com.arealytics.core.converter.LTSLeaseResearchStatusConverter;
import com.arealytics.core.converter.TSATenantResearchStatusConverter;
import com.arealytics.core.enumeration.LTSLeaseResearchStatus;
import com.arealytics.core.enumeration.TSATenantResearchStatus;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PropertyInternalToolFields {

    @Column(name = "ResearchTypeID")
    private Integer researchTypeID;

    @Column(name = "ResearchTypeName", length = 25)
    private String researchTypeName;

    @Lob
    @Column(name = "TrueOwners", columnDefinition = "TEXT")
    private String trueOwners;

    @Lob
    @Column(name = "RecordedOwners", columnDefinition = "TEXT")
    private String recordedOwners;

    @Column(name = "IsSkipped")
    private Boolean isSkipped;

    @Column(name = "IsMultiplePolygonsNeeded")
    private Boolean isMultiplePolygonsNeeded;

    @Column(name = "NeedsResearchComments", columnDefinition = "TEXT")
    private String needsResearchComments;

    @Column(
            name = "TSATenantResearchStatus",
            columnDefinition =
                    "ENUM('Grouping Completed','Grouping InProgress','Not Started','Merge"
                            + " InProgress','Merge Completed','Stacking InProgress','Stacking"
                            + " Completed')")
    @Convert(converter = TSATenantResearchStatusConverter.class)
    private TSATenantResearchStatus tsaTenantResearchStatus;

    @Column(
            name = "LTSLeaseResearchStatus",
            columnDefinition =
                    "ENUM('Grouping Completed','Grouping InProgress','Not Started','Merge"
                            + " Completed','Linked To Tenant','Merge InProgress','Linked To Tenant"
                            + " InProgress')")
    @Convert(converter = LTSLeaseResearchStatusConverter.class)
    private LTSLeaseResearchStatus ltsLeaseResearchStatus;
}
