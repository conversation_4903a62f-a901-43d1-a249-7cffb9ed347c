package com.arealytics.core.domain.empiricalProd;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

import jakarta.persistence.*;
import org.locationtech.jts.geom.Geometry;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "Market")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Market extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MarketID")
    private Integer marketId;

    @Column(name = "MarketName", length = 45)
    private String marketName;

    @Column(name = "IsActive", columnDefinition = "TINYINT(1) DEFAULT 1")
    private Boolean isActive = true;

    @Column(name = "MetroID")
    private Integer metroId;

    @Column(name = "Geography", length = 45)
    private String geography;

    @Column(name = "UseTypeID")
    private Integer useTypeId;

    @Column(name = "GeoShape", columnDefinition = "geometry NOT NULL")
    private Geometry geoShape;

    @Column(name = "CreatedDate")
    private Instant createdDate;

    @Column(name = "ModifiedDate")
    private Instant modifiedDate;

    @Column(name = "DisplayColor", length = 15)
    private String displayColor;

}
