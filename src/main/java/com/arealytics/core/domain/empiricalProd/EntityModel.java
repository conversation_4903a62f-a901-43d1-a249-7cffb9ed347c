package com.arealytics.core.domain.empiricalProd;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "Entity")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EntityModel {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "EntityID")
  private Integer entityId;

  @ManyToOne
  @JoinColumn(name = "PersonID")
  private Person person;

  @ManyToOne
  @JoinColumn(name = "CompanyID")
  private Company company;

  @Column(name = "TenantID")
  private Integer tenantId;

  @Column(name = "RoleID")
  private Integer roleId;

  @Column(name = "StartDate")
  private LocalDateTime startDate;

  @Column(name = "EndDate")
  private LocalDateTime endDate;

  @Column(name = "ApplicationID")
  private Integer applicationId;
}
