package com.arealytics.core.domain.empiricalProd;

import org.hibernate.envers.Audited;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "PropertyResearchStatus")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Audited(withModifiedFlag = true)
public class PropertyResearchStatus extends ALTransactionBaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer propertyResearchStatusId;

    @ManyToOne
    @JoinColumn(name = "propertyID")
    private Property property;

    @Column(name = "propertyResearchTypeId")
    private Integer propertyResearchTypeId;

    @Column(name = "IsActive")
    private Boolean isActive;
}
