package com.arealytics.core.domain.shared;

import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionEntity;

import com.arealytics.core.config.CustomRevisionListener;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "Revision_Info")
@RevisionEntity(CustomRevisionListener.class)
@Getter
@Setter
public class CustomRevisionEntity extends DefaultRevisionEntity {
    @Column(name = "changed_by")
    private Integer changedBy;

    @Column(name = "application_id")
    private Integer applicationId;
}
