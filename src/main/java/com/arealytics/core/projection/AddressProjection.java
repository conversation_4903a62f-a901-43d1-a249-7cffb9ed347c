package com.arealytics.core.projection;

import com.arealytics.core.domain.empiricalProd.QAddress;
import com.arealytics.core.dto.response.AddressDTO;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Projections;

public class AddressProjection {
    private static final QAddress address = QAddress.address;

    public static Expression<AddressDTO> projectAddressDTO() {
        return Projections.constructor(
                AddressDTO.class,
                address.addressId,
                address.addressTypeId,
                address.parentTableId,
                address.sequence,
                address.suffixId.as("streetSuffix1"),
                address.suffix2Id.as("streetSuffix2"),
                address.zip4,
                address.floorNumber,
                address.cityId,
                address.stateId,
                address.countryId,
                address.address1,
                address.address2,
                address.streetNumberMin,
                address.streetNumberMax,
                address.eastWestSt,
                address.northSouthSt,
                address.quadrantId,
                address.prefixId.as("streetPrefix1"),
                address.prefix2Id.as("streetPrefix2"),
                address.buildingNumber,
                address.partOfCenterComplex,
                address.complexName,
                address.primaryStreet,
                address.primaryAccess,
                address.primaryTrafficCount,
                address.primaryTrafficCountDate,
                address.primaryFrontage,
                address.secondaryStreet,
                address.secondaryAccess,
                address.secondaryTrafficCount,
                address.secondaryTrafficCountDate,
                address.secondaryFrontage);
    }
}
