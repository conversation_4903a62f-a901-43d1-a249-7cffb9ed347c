package com.arealytics.core.repository.empiricalProd;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.core.domain.empiricalProd.ParcelProperty;

@Repository
public interface ParcelPropertyRepository extends JpaRepository<ParcelProperty, Integer> {
    List<ParcelProperty> findByPropertyIdAndParcel_IsActive(Integer propertyId, boolean isActive);

    ParcelProperty findByParcelPropertyId(Integer parcelId);

    Optional<ParcelProperty> findByParcel_ParcelIdAndParcel_IsActive(
            Integer parcelId, boolean isActive);
}
