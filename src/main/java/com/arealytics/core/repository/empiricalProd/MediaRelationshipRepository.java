package com.arealytics.core.repository.empiricalProd;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.core.domain.empiricalProd.MediaRelationship;
import com.arealytics.core.enumeration.MediaRelationType;

@Repository
public interface MediaRelationshipRepository extends JpaRepository<MediaRelationship, Integer> {
  List<MediaRelationship> findByMediaRelationTypeIdAndRelationIdIn(
      MediaRelationType mediaRelationType, List<Integer> relationId);
}
