package com.arealytics.core.repository.empiricalProd;

import java.util.Optional;

import com.arealytics.core.repository.empiricalProd.custom.PropertyRepositoryCustom;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.core.domain.empiricalProd.Property;

@Repository
public interface PropertyRepository extends JpaRepository<Property, Integer>, PropertyRepositoryCustom {
    Optional<Property> findBypropertyID(Integer propertyId);
}
