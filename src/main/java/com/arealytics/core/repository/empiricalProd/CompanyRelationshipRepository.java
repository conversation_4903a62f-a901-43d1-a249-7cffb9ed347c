package com.arealytics.core.repository.empiricalProd;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.core.domain.empiricalProd.CompanyRelationship;

@Repository
public interface CompanyRelationshipRepository extends JpaRepository<CompanyRelationship, Integer> {
  List<CompanyRelationship> findByChildCompany_CompanyIdInAndIsActive(
      List<Integer> childCompanyId, Boolean isActive);
}
