package com.arealytics.core.repository.empiricalProd.custom;

import com.arealytics.core.dto.querydsl.PropertyDetailsQuerydslDTO;
import com.arealytics.core.dto.querydsl.PropertyMapSearchQuerydslDTO;
import com.arealytics.core.dto.querydsl.PropertySearchQuerydslDTO;
import com.arealytics.core.dto.request.PropertyMapSearchRequestDTO;
import com.arealytics.core.dto.request.PropertySearchRequestDTO;
import com.arealytics.core.dto.querydsl.MasterPropertiesDTO;
import com.arealytics.core.dto.response.PropertyDTO;
import com.arealytics.core.enumeration.CondoType;

import org.springframework.data.domain.Pageable;

import java.util.List;

public interface PropertyRepositoryCustom {
    PropertyDetailsQuerydslDTO findPropertyDetailsByPropertyID(Integer propertyId);
    List<PropertySearchQuerydslDTO> findPropertiesBySearch(PropertySearchRequestDTO searchCriteria,Pageable pageable);
    Long countBySearchCriteria(PropertySearchRequestDTO searchCriteria);
    List<PropertyMapSearchQuerydslDTO> findPropertiesByMapSearch(PropertyMapSearchRequestDTO searchCriteria, Pageable pageable);
    Long countByMapSearchCriteria(PropertyMapSearchRequestDTO searchCriteria);
    List<MasterPropertiesDTO> findMasterPropertiesBySearch(String searchText, String unit, CondoType strataType);
    List<PropertyDTO> findStrataPropertiesByMasterStrataProperty(Integer masterPropertyID);
}
