package com.arealytics.core.repository.empiricalProd;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.arealytics.core.domain.empiricalProd.PropertyStrataRelationship;

public interface PropertyStrataRelationshipRepository
    extends JpaRepository<PropertyStrataRelationship, Integer> {

  // Find active relationship by StrataPropertyID
  Optional<PropertyStrataRelationship> findByStrataProperty_PropertyIDAndIsActive(
      Integer strataPropertyID, Boolean isActive);

  // Find active relationship by MasterPropertyID
  List<PropertyStrataRelationship> findByMasterProperty_PropertyIDAndIsActive(
      Integer masterPropertyID, Boolean isActive);

  // Find all relationships by MasterPropertyID and isActive status
  List<PropertyStrataRelationship> findAllByMasterProperty_PropertyIDAndIsActive(
      Integer masterPropertyID, Boolean isActive);

  // Check if an active relationship exists with a different MasterPropertyID
  boolean existsByStrataProperty_PropertyIDAndIsActiveAndMasterProperty_PropertyIDNot(
      Integer strataPropertyID, Boolean isActive, Integer masterPropertyID);

  // Check if an active relationship exists for StrataPropertyID
  boolean existsByStrataProperty_PropertyIDAndIsActive(
      Integer strataPropertyID, Boolean isActive);
}
