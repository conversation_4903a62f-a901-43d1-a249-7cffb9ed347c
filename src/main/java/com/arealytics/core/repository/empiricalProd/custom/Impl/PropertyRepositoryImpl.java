package com.arealytics.core.repository.empiricalProd.custom.Impl;

import com.arealytics.core.common.querydsl.QuerydslRepositorySupportBase;
import com.arealytics.core.common.querydsl.predicate.PredicateConditions;
import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.dto.querydsl.PropertyDetailsQuerydslDTO;
import com.arealytics.core.dto.querydsl.PropertyMapSearchQuerydslDTO;
import com.arealytics.core.dto.querydsl.PropertySearchQuerydslDTO;
import com.arealytics.core.dto.request.PropertyMapSearchRequestDTO;
import com.arealytics.core.dto.request.PropertySearchRequestDTO;
import com.arealytics.core.dto.response.PropertyDTO;
import com.arealytics.core.enumeration.CondoType;
import com.arealytics.core.projection.AddressProjection;
import com.arealytics.core.projection.LocationProjection;
import com.arealytics.core.repository.empiricalProd.custom.PropertyRepositoryCustom;
import static com.arealytics.core.common.querydsl.query.PropertySearchBaseQueryBuilder.buildSearchBaseQuery;

import com.arealytics.core.utils.UnitConversionUtil;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.arealytics.core.utils.SqlExpressionUtil;
import com.querydsl.core.types.dsl.Expressions;

import jakarta.persistence.EntityManager;
import org.springframework.data.domain.Pageable;

import java.util.List;

import static com.arealytics.core.common.querydsl.join.JoinConditions.*;
import static com.arealytics.core.utils.UnitConversionUtil.SQFT_TO_SQM_FACTOR;

import com.arealytics.core.dto.querydsl.MasterPropertiesDTO;

public class PropertyRepositoryImpl extends QuerydslRepositorySupportBase<Property>
        implements PropertyRepositoryCustom {

    private final JPAQueryFactory jpaQueryFactory;

    private final QProperty property = QProperty.property;
    private final QAddress address = QAddress.address;
    private final QLocation location = QLocation.location;
    private final QCity city = QCity.city;
    private final QState state = QState.state;
    private final QUse use = QUse.use;    private final QMediaRelationship mediaRelationship = QMediaRelationship.mediaRelationship;

    private final QMarket market = QMarket.market;
    private final QMetro metro = QMetro.metro;
    private final QSpecificUses specificUses = QSpecificUses.specificUses;
    private final QCounty county = QCounty.county ;
    private final QCountry country = QCountry.country ;
    private final QMedia media = QMedia.media;
    private final QEntityModel createdEntity = QEntityModel.entityModel;
    private final QEntityModel modifiedEntity = new QEntityModel("modifiedEntity");
    private final QEntityModel lastReviewedEntity = new QEntityModel("lastReviewedEntity");
    private final QPerson createdPerson = QPerson.person;
    private final QPerson modifiedPerson = new QPerson("modifiedPerson");
    private final QPerson lastReviewedPerson = new QPerson("lastReviewedPerson");
    private final QSubMarket subMarket = QSubMarket.subMarket;
    private final QPropertyStrataRelationship propertyStrataRelationship = QPropertyStrataRelationship.propertyStrataRelationship;
    private final QPropertyFeature propertyFeature = QPropertyFeature.propertyFeature;
    private final QAmenities amenities = QAmenities.amenities;
    private final QPropertyAuditLog propertyAuditLog = QPropertyAuditLog.propertyAuditLog;
    private final QStatusDefination statusDefination = QStatusDefination.statusDefination;
    private final QUseType useType = QUseType.useType;

    private final PredicateConditions predicateBuilder = new PredicateConditions();
    public PropertyRepositoryImpl(EntityManager entityManager, JPAQueryFactory queryFactory) {
        super(Property.class, queryFactory);
        this.jpaQueryFactory = new JPAQueryFactory(entityManager);
    }

    @Override
    public PropertyDetailsQuerydslDTO findPropertyDetailsByPropertyID(Integer propertyId) {
        JPQLQuery<String> featureIdSubQuery = JPAExpressions
                .select(Expressions.stringTemplate(
                        "group_concat({0})", propertyFeature.featureId))
                .from(propertyFeature)
                .where(
                        propertyFeature.propertyId.eq(propertyId),
                        propertyFeature.isActive.eq(true)
                );


        JPQLQuery<String> amenitiesTypeSubQuery = JPAExpressions
                .select(Expressions.stringTemplate(
                        "group_concat({0})", amenities.amenitiesTypeId))
                .from(amenities)
                .where(
                        amenities.propertyId.eq(propertyId),
                        amenities.isActive.eq(true)
                );

        return fetchOne(
                factory ->
                        factory.select(
                                        Projections.constructor(
                                                PropertyDetailsQuerydslDTO.class,
                                                property.propertyID,
                                                property.propertyName,
                                                property.parentPropertyID,
                                                property.isActive,
                                                property.propertyDetails.yearBuilt,
                                                property.propertyDetails.yearRenovated,
                                                property.propertyDetails.floors,
                                                property.propertyDetails.constructionStatusID,
                                                property.propertyDetails.constructionTypeID,
                                                property.propertyDetails.hvacTypeID,
                                                property.propertyDetails.sprinklerTypeID,
                                                property.propertyDetails.condoTypeID,
                                                property.propertyDetails.condoUnit,
                                                property.propertyDetails.useTypeID,
                                                property.propertyDetails.useTypeName,
                                                property.propertyDetails.specificUseID,
                                                property.propertyDetails.specificUseName,
                                                property.propertyDetails.estimatedCompletionDate,
                                                property.propertyDetails.isADAAccessible,
                                                property.propertyDetails.isVented,
                                                property.propertyDetails.isOwnerOccupied,
                                                property.propertyDetails.classTypeID,
                                                property.propertyDetails.tenancyTypeID,
                                                property.propertyDetails.isEnergyStar,
                                                property.propertySize.NRASizeSourceID,
                                                property.propertyDetails.mixedUseAllocation,
                                                property.propertyDetails.buildingWebsite,
                                                property.propertyDetails.buildingComments,
                                                property.propertyDetails.noOfOfficeFloors,
                                                property.propertyLocation.metroId,
                                                property.propertyLocation.marketId,
                                                subMarket.subMarketId,
                                                property.propertyDetails.governmentInterestID,
                                                property.propertyDetails.hasSolar,
                                                property.propertyDetails.trafficCount,
                                                property.propertyDetails.energyStarRatingID,
                                                property.propertyDetails.waterStarRatingID,
                                                property.propertyDetails.greenStarRatingID,
                                                property.propertyDetails.occupiedPercentage,
                                                property.propertyDetails.directOccupiedPercentage,
                                                property.propertyDetails.currentTitle,
                                                property.propertyDetails.tiAllowance,
                                                property.propertyDetails.gresbScore,
                                                property.propertyDetails.gresbScoreMin,
                                                property.propertyDetails.gresbScoreMax,
                                                property.propertyDetails.actualCompletion,
                                                property.propertyDetails.titleReferenceDate,
                                                property.propertyDetails.landUse,
                                                property.propertyDetails.lastReviewedBy,
                                                property.propertyDetails.lastReviewedDate,
                                                property.propertyDetails.constructionStartDate,
                                                property.propertyDetails.estCompletion,
                                                property.propertyDetails.bookValue,
                                                property.propertyDetails.bookValueDate,
                                                property.propertyDetails.parcelInfo,
                                                property.propertyDetails.mainPhotoUrl,
                                                property.propertyLocation.parkComplexName,
                                                property.propertyLocation.zoning,
                                                property.propertyLocation.lga,
                                                property.propertyLocation.useAddressAsPropertyName,
                                                property.amenities.amenities,
                                                property.amenities.passengerElevators,
                                                property.amenities.parkingElevators,
                                                property.amenities.freighElevators,
                                                property.amenities.dockHigh,
                                                property.amenities.gradeLevelIn,
                                                property.amenities.truckWell,
                                                property.amenities.yardPaved,
                                                property.amenities.reservedParkingSpaces,
                                                property.amenities.reservedParkingSpacesRatePerMonth,
                                                property.amenities.hasReservedParkingSpaces,
                                                property.amenities.unreservedParkingSpaces,
                                                property.amenities.unreservedParkingSpacesRatePerMonth,
                                                property.amenities.hasUnreservedParkingSpaces,
                                                property.amenities.powerComments,
                                                property.amenities.utilityComments,
                                                property.amenities.propertyComments,
                                                property.amenities.noOfUnits,
                                                property.amenities.totalAnchor,
                                                property.amenities.hvac,
                                                property.amenities.lifts,
                                                property.amenities.liftsCount,
                                                property.amenities.powerType,
                                                property.amenities.vacancy,
                                                property.amenities.bayWidth,
                                                property.amenities.bayDepth,
                                                property.amenities.depth,
                                                property.amenities.width,
                                                property.amenities.includeInAnalytics,
                                                property.propertySize.GBASizeSource,
                                                property.amenities.officeAC,
                                                property.propertySize.lotSizeSourceID,
                                                property.propertyLocation.zoningClassID,
                                                property.propertyLocation.zoningCode,
                                                property.amenities.potentialZoningID,
                                                property.propertyLocation.surroundingLandUse,
                                                property.amenities.railServed,
                                                property.amenities.isFloodPlain,
                                                property.propertySize.smallestFloor,
                                                property.propertySize.largestFloor,
                                                property.propertyDetails.roofTypeID,
                                                property.propertyDetails.craneServed,
                                                property.amenities.hasYardFenced,
                                                property.amenities.hasYardUnfenced,
                                                property.amenities.buildSpecStatusID,
                                                property.propertyLocation.legalDescription,
                                                property.amenities.internalComments,
                                                property.amenities.parkingSpaces,
                                                property.amenities.hasSprinkler,
                                                property.propertySize.sizeSourceID,
                                                property.amenities.noOfAnchor,
                                                property.amenities.hasPortAccess,
                                                property.amenities.hasYard,
                                                property.amenities.hasResCoveredParking,
                                                property.amenities.parkingRatio,
                                                property.propertyDetails.earthquakeZoneID,
                                                property.propertyLocation.geoscapePropertyID,
                                                property.propertyLocation.counsilTaxID,
                                                property.propertyLocation.valuerGeneralID,
                                                property.propertySize.clearHeightMin,
                                                property.propertySize.clearHeightMax,
                                                property.amenities.volts,
                                                property.amenities.phase,
                                                property.amenities.amps,
                                                property.propertySize.retailFrontage,
                                                property.propertySize.typicalFloorSize,
                                                property.propertySize.hardstandArea,
                                                property.propertySize.hardstandAreaSourceID,
                                                property.propertySize.contributedGBASizeSourceID,
                                                property.propertySize.glasSizeSourceID,
                                                property.propertySize.glarSizeSourceID,
                                                property.propertySize.mezzanine,
                                                property.propertySize.awnings,
                                                property.propertySize.awningsCount,
                                                property.propertySize.buildingSize,
                                                property.propertySize.lotSize,
                                                property.propertySize.minFloorSize,
                                                property.propertySize.maxFloorSize,
                                                property.propertySize.retailSize,
                                                property.propertySize.officeSize,
                                                property.propertySize.buildingSizeSF,
                                                property.propertySize.lotSizeSF,
                                                property.propertySize.totalSaleSizeSF,
                                                property.propertySize.contributedGBASizeSF,
                                                property.propertySize.glaSizeSF,
                                                property.propertySize.glarSizeSF,
                                                property.propertySize.mezzanineSizeSF,
                                                property.propertySize.awningsSizeSF,
                                                property.propertySize.lotSizeAC,
                                                property.propertySize.nla,
                                                property.propertySize.nlaac,
                                                property.propertySize.contributedGBA,
                                                property.propertySize.gla,
                                                property.propertySize.glar,
                                                property.internalToolFields.researchTypeID,
                                                property.internalToolFields.researchTypeName,
                                                property.internalToolFields.trueOwners,
                                                property.internalToolFields.recordedOwners,
                                                property.internalToolFields.isSkipped,
                                                property.internalToolFields.isMultiplePolygonsNeeded,
                                                property.internalToolFields.needsResearchComments,
                                                property.propertyDetails.hasNoBuildingFootprints,
                                                property.propertyDetails.hasNoExistingParcelInTileLayer,
                                                propertyStrataRelationship.masterPropertyId,
                                                SqlExpressionUtil.concat(true, createdPerson.firstName, createdPerson.lastName).as("propertyCreatedByName"),
                                                SqlExpressionUtil.concat(true, modifiedPerson.firstName, modifiedPerson.lastName).as("propertyModifiedByName"),
                                                SqlExpressionUtil.concat(true, modifiedPerson.firstName, modifiedPerson.lastName).as("propertyLastReviewedByName"),
                                                property.propertyDetails.contributedSourceComments,
                                                property.propertySize.typicalFloorSizeSourceId,
                                                property.propertyDetails.officeHVAC,
                                                featureIdSubQuery,
                                                amenitiesTypeSubQuery,
                                                AddressProjection.projectAddressDTO(),
                                                LocationProjection.projectLocationDTO()))
                                .from(property)
                                .join(property.addresses, address)
                                .on(joinAddressOnPropertyPrimaryAddress(address, property))
                                .join(address.location, location)
                                .join(address.city, city)
                                .join(address.state, state)
                                .leftJoin(propertyStrataRelationship).on(JoinPropertyStrataRelationshipOnProperty(propertyStrataRelationship, property))
                                .leftJoin(property.genuses, use).on(joinUseOnPropertyPrimaryUse(use, property))
                                .leftJoin(market).on(JoinMarketOnProperty(market, property))
                                .leftJoin(subMarket).on(JoinSubMarketOnProperty(subMarket, property))
                                .leftJoin(metro).on(JoinMetroOnMarket(metro, market))
                                .leftJoin(specificUses).on(JoinSpecificUsesOnUse(specificUses, use))
                                .leftJoin(county).on(JoinCountyOnAddress(county, address))
                                .leftJoin(country).on(JoinCountryOnAddress(country, address))
                                .leftJoin(mediaRelationship).on(JoinMediaRelationshipOnProperty(mediaRelationship, property))
                                .leftJoin(media).on(JoinMediaOnMediaRelationship(media, mediaRelationship))
                                .leftJoin(createdEntity).on(JoinEntityModelOnPropertyCreatedBy(createdEntity, property))
                                .leftJoin(createdPerson).on(JoinPersonOnEntityModel(createdPerson, createdEntity))
                                .leftJoin(modifiedEntity).on(JoinEntityModelOnPropertyModifiedBy(modifiedEntity, property))
                                .leftJoin(modifiedPerson).on(JoinPersonOnEntityModel(modifiedPerson, modifiedEntity))
                                .leftJoin(lastReviewedEntity).on(JoinEntityModelOnPropertyLastReviewedBy(lastReviewedEntity, property))
                                .leftJoin(lastReviewedPerson).on(JoinPersonOnEntityModel(lastReviewedPerson, lastReviewedEntity))
                                .where(property.propertyID.eq(propertyId)));
    }

    public List<PropertySearchQuerydslDTO> findPropertiesBySearch(PropertySearchRequestDTO searchCriteria, Pageable pageable) {

        BooleanBuilder predicate = predicateBuilder.searchBuild(searchCriteria, property, address, city, state, statusDefination);

        return buildSearchBaseQuery(jpaQueryFactory,predicate)
                        .select(Projections.constructor(
                                        PropertySearchQuerydslDTO.class,
                                        property.propertyID,
                                        property.propertyName,
                                        property.isActive,
                                        property.propertyDetails.yearBuilt,
                                        property.propertyDetails.floors,
                                        property.propertyDetails.condoTypeID,
                                        property.propertyDetails.condoUnit,
                                        property.propertyDetails.useTypeID,
                                        property.propertyDetails.useTypeName,
                                        property.propertyDetails.specificUseID,
                                        property.propertyDetails.specificUseName,
                                        property.propertyDetails.buildingComments,
                                        property.propertyDetails.lastReviewedBy,
                                        property.propertyDetails.lastReviewedDate,
                                        property.propertySize.buildingSize,
                                        property.propertySize.lotSize,
                                        property.propertySize.buildingSizeSF,
                                        property.propertySize.lotSizeSF,
                                        property.propertySize.contributedGBASizeSF,
                                        property.internalToolFields.researchTypeID,
                                        property.internalToolFields.researchTypeName,
                                        property.internalToolFields.isSkipped,
                                        property.propertyDetails.mainPhotoUrl,
                                        property.propertyDetails.hasNoBuildingFootprints,
                                        property.propertyDetails.hasNoExistingParcelInTileLayer,
                                        propertyStrataRelationship.masterPropertyId,
                                        AddressProjection.projectAddressDTO(),
                                        LocationProjection.projectLocationDTO(),
                                        property.createdDate,
                                        property.modifiedDate,
                                        property.CreatedBy.entityId,
                                        property.modifiedBy.entityId,
                                        statusDefination.statusName,
                                        city.cityName
                                ))
                                .offset(pageable.getOffset())
                                .limit(pageable.getPageSize())
                .fetch();

    }

    public Long countBySearchCriteria(PropertySearchRequestDTO searchCriteria) {

        BooleanBuilder predicate = predicateBuilder.searchBuild(searchCriteria, property, address, city, state, statusDefination);

        return buildSearchBaseQuery(jpaQueryFactory,predicate)
                .select(property.count())
                .fetchOne();
    }

    public List<PropertyMapSearchQuerydslDTO> findPropertiesByMapSearch(PropertyMapSearchRequestDTO searchCriteria, Pageable pageable) {

        BooleanBuilder predicate = predicateBuilder.mapSearchBuild(searchCriteria, property, address, location);

        return buildSearchBaseQuery(jpaQueryFactory, predicate)
                .select(Projections.constructor(
                        PropertyMapSearchQuerydslDTO.class,
                                        property.propertyID,
                                        property.propertyName,
                                        property.propertyDetails.yearBuilt,
                                        property.propertyDetails.condoTypeID,
                                        property.propertyDetails.condoUnit,
                                        property.propertyDetails.useTypeID,
                                        property.propertyDetails.useTypeName,
                                        property.propertyDetails.specificUseID,
                                        property.propertyDetails.specificUseName,
                                        property.propertySize.buildingSizeSF,
                                        property.propertySize.lotSizeSF,
                                        property.propertySize.contributedGBASizeSF,
                                        property.internalToolFields.researchTypeID,
                                        property.internalToolFields.researchTypeName,
                                        property.propertyDetails.mainPhotoUrl,
                                        property.propertyDetails.hasNoBuildingFootprints,
                                        AddressProjection.projectAddressDTO(),
                                        LocationProjection.projectLocationDTO(),
                                        city.cityName,
                                        state.stateAbbr,
                                        country.alpha3Code,
                                        property.propertyDetails.parcelInfo,
                                        property.internalToolFields.trueOwners,
                                        property.internalToolFields.recordedOwners,
                                        property.propertyDetails.classTypeID,
                                        property.propertyDetails.constructionStatusID
                                ))
                                .offset(pageable.getOffset())
                                .limit(pageable.getPageSize())
                                .fetch();

      }

    public List<MasterPropertiesDTO> findMasterPropertiesBySearch(String searchText, String unit,
          CondoType strataType) {
            BooleanBuilder predicate = predicateBuilder.masterSearchBuilder(searchText, unit, strataType, property, address);

        return fetch(
            factory -> factory.select(Projections.constructor(MasterPropertiesDTO.class,
                property.propertyID,
                property.propertyName,
                address.addressText,
                city.cityId,
                city.cityName,
                state.stateId,
                state.stateName,
                state.stateAbbr,
                country.countryId,
                country.countryName,
                address.zipCode,
                            useType.useTypeName.as("propertyUse"),
                property.propertyDetails.parcelInfo,
                property.propertySize.buildingSizeSF.as("buildingSF"),
                UnitConversionUtil.sqftToSqmConversion( property.propertySize.buildingSizeSF).as("buildingSM"),
                SqlExpressionUtil.concat(true, property.propertyName, property.propertyID.stringValue())
                    .as("propertyNameDisplay"),
                property.internalToolFields.researchTypeID.as("researchTypeID"),
                property.internalToolFields.researchTypeName.as("researchTypeName")))
                .from(property)
                .join(property.addresses, address)
                .on(joinAddressOnPropertyPrimaryAddress(address, property))
                .join(address.city, city)
                .join(address.state, state)
                .leftJoin(country).on(JoinCountryOnAddress(country, address))
                    .leftJoin(useType).on(JoinUseTypeOnProperty(useType, property))
                .where(predicate));
    }

    public Long countByMapSearchCriteria(PropertyMapSearchRequestDTO searchCriteria) {

        BooleanBuilder predicate = predicateBuilder.mapSearchBuild(searchCriteria, property, address, location);

        return buildSearchBaseQuery(jpaQueryFactory,predicate)
                .select(property.count())
                .fetchOne();
    }

    public List<PropertyDTO> findStrataPropertiesByMasterStrataProperty(Integer masterProperty) {

        return fetch(
                factory -> factory.select(Projections.constructor(
                        PropertyDTO.class,
                                property.propertyID,
                                property.propertyName,
                                property.propertyDetails.yearBuilt,
                                property.propertyDetails.floors,
                                property.propertyDetails.condoTypeID,
                                property.propertyDetails.condoUnit,
                                property.propertyDetails.useTypeID,
                                property.propertyDetails.useTypeName,
                                property.propertyDetails.specificUseID,
                                property.propertyDetails.specificUseName,
                                property.propertyDetails.buildingComments,
                                property.propertySize.buildingSizeSF,
                                property.propertySize.buildingSizeSF.multiply(UnitConversionUtil.SQFT_TO_SQM_FACTOR),
                                property.internalToolFields.researchTypeID,
                                property.internalToolFields.researchTypeName,
                                property.internalToolFields.isSkipped,
                                property.propertyDetails.mainPhotoUrl,
                                statusDefination.statusName,
                                property.propertyDetails.lastReviewedBy,
                                address.addressText,
                                city.cityName,
                                state.stateName,
                                county.countyName,
                                country.countryName,
                                address.zipCode,
                                location.latitude,
                                location.longitude,
                                address.streetNumberMin,
                                address.streetNumberMax,
                                address.addressStreetName))
                        .from(property)
                        .join(propertyStrataRelationship).on(JoinPropertyStrataRelationshipOnProperty(propertyStrataRelationship, property))
                        .join(property.addresses, address).on(joinAddressOnPropertyPrimaryAddress(address, property))
                        .join(address.city, city)
                        .join(address.state, state)
                        .join(county).on(JoinCountyOnAddress(county,address))
                        .leftJoin(country).on(JoinCountryOnAddress(country, address))
                        .leftJoin(propertyAuditLog).on(JoinPropertyAuditLogOnProperty(propertyAuditLog, property))
                        .leftJoin(statusDefination).on(JoinStatusDefinationOnPropertyAuditLog(statusDefination, propertyAuditLog))
                        .where(propertyStrataRelationship.isActive.eq(true).and(propertyStrataRelationship.masterPropertyId.eq(masterProperty)))
        );
    }

}
