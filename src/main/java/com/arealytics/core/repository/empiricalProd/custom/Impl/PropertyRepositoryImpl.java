package com.arealytics.core.repository.empiricalProd.custom.Impl;

import java.util.List;

import com.arealytics.core.common.querydsl.QuerydslRepositorySupportBase;
import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.dto.response.PropertyDetailsResponseDTO;
import com.arealytics.core.projection.AddressProjection;
import com.arealytics.core.repository.empiricalProd.custom.PropertyRepositoryCustom;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;

import static com.arealytics.core.common.querydsl.join.JoinConditions.*;

public class PropertyRepositoryImpl extends QuerydslRepositorySupportBase<Property>
        implements PropertyRepositoryCustom {

    private final QProperty property = QProperty.property;
    private final QAddress address = QAddress.address;
    private final QLocation location = QLocation.location;
    private final QCity city = QCity.city;
    private final QState state = QState.state;
    private final QUse use = QUse.use;
    private final QMarket market = QMarket.market;
    private final QMetro metro = QMetro.metro;
    private final QSpecificUses specificUses = QSpecificUses.specificUses;
    private final QCounty county = QCounty.county ;
    private final QCountry country = QCountry.country ;
    private final QMediaRelationship mediaRelationship = QMediaRelationship.mediaRelationship;
    private final QMedia media = QMedia.media;
    private final QEntityModel entityModel1 = QEntityModel.entityModel;
    private final QEntityModel entityModel2 = QEntityModel.entityModel;
    private final QEntityModel entityModel3 = QEntityModel.entityModel;
    private final QPerson person1 = QPerson.person;
    private final QPerson person2 = QPerson.person;
    private final QPerson person3 = QPerson.person;
    private final QSubMarket subMarket = QSubMarket.subMarket;

    public PropertyRepositoryImpl(JPAQueryFactory queryFactory) {
        super(Property.class, queryFactory);
    }

    @Override
    public PropertyDetailsResponseDTO findPropertyDetailsByPropertyID(Integer propertyId) {
        return fetchOne(
                factory ->
                        factory.select(
                                        Projections.constructor(
                                                PropertyDetailsResponseDTO.class,
                                                property.propertyID,
                                                property.propertyName,
                                                property.propertyDetails.constructionStatusID,
                                                property.propertyDetails.constructionTypeID,
                                                property.propertyDetails.hvacTypeID,
                                                property.propertyDetails.sprinklerTypeID,
                                                property.propertyDetails.estimatedCompletionDate,
                                                property.propertyDetails.isADAAccessible,
                                                property.propertyDetails.isVented,
                                                property.propertyDetails.isOwnerOccupied,
                                                property.propertyDetails.tenancyTypeID,
                                                property.propertyDetails.isEnergyStar,
                                                property.propertySize.NRASizeSourceID,
                                                property.propertyDetails.buildingComments,
                                                property.location.metroId,
                                                property.location.marketId,
                                                property.propertyDetails.governmentInterestID,
                                                property.propertyDetails.constructionStartDate,
                                                property.propertyDetails.estCompletion,
                                                property.propertyDetails.noOfOfficeFloors,
                                                property.amenities.passengerElevators,
                                                property.amenities.passengerElevators,
                                                property.amenities.freighElevators,
                                                property.amenities.dockHigh,
                                                property.amenities.gradeLevelIn,
                                                property.amenities.truckWell,
                                                property.amenities.powerComments,
                                                property.amenities.bayWidth,
                                                property.amenities.bayDepth,
                                                property.amenities.depth,
                                                property.amenities.width,
                                                property.amenities.includeInAnalytics,
                                                property.propertySize.GBASizeSource,
                                                property.amenities.officeAC,
                                                property.propertySize.lotSizeSourceID,
                                                property.location.zoningClassID,
                                                property.location.zoningCode,
                                                property.amenities.potentialZoningID,
                                                property.location.surroundingLandUse,
                                                property.amenities.railServed,
                                                property.amenities.isFloodPlain,
                                                property.propertySize.smallestFloor,
                                                property.propertySize.largestFloor,
                                                property.propertyDetails.roofTypeID,
                                                property.propertyDetails.craneServed,
                                                property.amenities.hasYardFenced,
                                                property.amenities.hasYardUnfenced,
                                                property.amenities.buildSpecStatusID,
                                                property.location.legalDescription,
                                                property.propertyDetails.condoTypeID,
                                                property.amenities.internalComments,
                                                property.propertyDetails.yearBuilt,
                                                property.propertyDetails.yearRenovated,
                                                property.amenities.parkingSpaces,
                                                property.amenities.hasSprinkler,
                                                property.propertySize.sizeSourceID,
                                                property.amenities.noOfAnchor,
                                                property.amenities.hasPortAccess,
                                                property.amenities.hasYard,
                                                property.amenities.hasResCoveredParking,
                                                property.amenities.parkingRatio,
                                                property.propertyDetails.earthquakeZoneID,
                                                property.location.geoscapePropertyID,
                                                property.location.counsilTaxID,
                                                property.location.valuerGeneralID,
                                                property.propertySize.clearHeightMin,
                                                property.propertySize.clearHeightMax,
                                                property.amenities.volts,
                                                property.amenities.phase,
                                                property.amenities.amps,
                                                AddressProjection.projectAddressDTO()))
                                .from(property)
                                .join(property.addresses, address)
                                .on(joinAddressOnProperty(address, property))
                                .join(address.location, location)
                                .join(address.city, city)
                                .join(address.state, state)
                                .leftJoin(property.genuses, use).on(joinCityOnProperty(use))
                                .leftJoin(market).on(JoinMarketOnProperty(market, property))
                                .leftJoin(subMarket).on(JoinSubMarketOnProperty(subMarket, property))
                                .leftJoin(metro).on(JoinMetroOnMarket(metro, market))
                                .leftJoin(specificUses).on(JoinSpecificUsesOnUse(specificUses, use))
                                .leftJoin(county).on(JoinCountyOnAddress(county, address))
                                .leftJoin(country).on(JoinCountryOnAddress(country, address))
                                .leftJoin(mediaRelationship).on(JoinMediaRelationshipOnProperty(mediaRelationship, property))
                                .leftJoin(media).on(JoinMediaOnMediaRelationship(media, mediaRelationship))
                                .leftJoin(entityModel1).on(JoinEntityModelOnPropertyModifiedBy(entityModel1, property))
                                .leftJoin(person1).on(JoinPersonOnEntityModel(person1, entityModel1))
                                .leftJoin(entityModel2).on(JoinEntityModelOnPropertyCreatedBy(entityModel2, property))
                                .leftJoin(person2).on(JoinPersonOnEntityModel(person2, entityModel2))
                                .leftJoin(entityModel3).on(JoinEntityModelOnPropertyLastReviewedBy(entityModel3, property))
                                .leftJoin(person3).on(JoinPersonOnEntityModel(person3, entityModel3))
                                .where(property.propertyID.eq(propertyId)));
    }
}
