package com.arealytics.core.repository.empiricalGIS;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.core.domain.empiricalGIS.BuildingFootPrint;

@Repository
public interface BuildingFootPrintRepository extends JpaRepository<BuildingFootPrint, Integer> {
    List<BuildingFootPrint> findByCrePropertyIdAndIsActiveTrueOrderByIsDefaultDesc(
            Integer propertyID);

    List<BuildingFootPrint> findByCrePropertyId(Integer crePropertyId);

    List<BuildingFootPrint> findByBuildingFootPrintIdInAndCrePropertyId(
            List<Integer> ids, Integer crePropertyId);

    List<BuildingFootPrint> findByBuildingFootPrintId(long buildingFootPrintId);
}
