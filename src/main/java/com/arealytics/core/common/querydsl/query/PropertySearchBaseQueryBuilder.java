package com.arealytics.core.common.querydsl.query;

import com.arealytics.core.domain.empiricalProd.*;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;

import static com.arealytics.core.common.querydsl.join.JoinConditions.*;

public class PropertySearchBaseQueryBuilder {

    private static final QProperty property = QProperty.property;
    private static final QAddress address = QAddress.address;
    private static final QLocation location = QLocation.location;
    private static final QCity city = QCity.city;
    private static final QState state = QState.state;
    private static final QEntityModel createdEntity = QEntityModel.entityModel;
    private static final QEntityModel modifiedEntity = new QEntityModel("modifiedEntity");
    private static final QPerson createdPerson = QPerson.person;
    private static final QPerson modifiedPerson = new QPerson("modifiedPerson");
    private static final QCounty county = QCounty.county;
    private static final QCountry country = QCountry.country;
    private static final QPropertyStrataRelationship propertyStrataRelationship = QPropertyStrataRelationship.propertyStrataRelationship;
    private static final QPropertyAuditLog propertyAuditLog = QPropertyAuditLog.propertyAuditLog;
    private static final QStatusDefination statusDefination = QStatusDefination.statusDefination;

    public static JPAQuery<?> buildSearchBaseQuery(JPAQueryFactory factory, BooleanBuilder predicate) {
        return factory.from(property)
                .join(property.addresses, address).on(joinAddressOnPropertyPrimaryAddress(address, property))
                .join(address.location, location)
                .join(address.city, city)
                .join(address.state, state)
                .leftJoin(propertyStrataRelationship).on(JoinPropertyStrataRelationshipOnProperty(propertyStrataRelationship, property))
                .leftJoin(county).on(JoinCountyOnAddress(county, address))
                .leftJoin(country).on(JoinCountryOnAddress(country, address))
                .leftJoin(createdEntity).on(JoinEntityModelOnPropertyModifiedBy(createdEntity, property))
                .leftJoin(createdPerson).on(JoinPersonOnEntityModel(createdPerson, createdEntity))
                .leftJoin(modifiedEntity).on(JoinEntityModelOnPropertyCreatedBy(modifiedEntity, property))
                .leftJoin(modifiedPerson).on(JoinPersonOnEntityModel(modifiedPerson, modifiedEntity))
                .leftJoin(propertyAuditLog).on(JoinPropertyAuditLogOnProperty(propertyAuditLog, property))
                .leftJoin(statusDefination).on(JoinStatusDefinationOnPropertyAuditLog(statusDefination, propertyAuditLog))
                .where(predicate);
    }
}
