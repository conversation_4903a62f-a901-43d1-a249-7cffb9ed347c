package com.arealytics.core.common.querydsl.join;

import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.enumeration.ParentTable;
import com.querydsl.core.types.dsl.BooleanExpression;

public class JoinConditions {
    public static BooleanExpression joinAddressOnPropertyPrimaryAddress(QAddress address, QProperty property) {
        return address.parentTableId
                .eq(ParentTable.Property)
                .and(address.isActive.eq(true))
                .and(address.sequence.eq(1));
    }

    public static BooleanExpression joinUseOnPropertyPrimaryUse(QUse use) {
        return use.parentTableId.eq(ParentTable.Property).and( use.sequence.eq(1));
    }

    public static BooleanExpression JoinMarketOnProperty(QMarket market,  QProperty property) {
        return market.marketId.eq(property.propertyLocation.marketId);
    }

    public static BooleanExpression JoinSubMarketOnProperty(QSubMarket subMarket, QProperty property) {
        return subMarket.subMarketId.eq(property.propertyLocation.subMarketID.subMarketId);
    }

    public static BooleanExpression JoinMetroOnMarket(QMetro metro, QMarket market) {
        return metro.metroId.eq(market.metroId);
    }

    public static BooleanExpression JoinSpecificUsesOnUse(QSpecificUses specificUses, QUse use) {
        return specificUses.specificUsesId.eq(use.SpecificUsesID);
    }

    public static BooleanExpression JoinCountyOnAddress(QCounty county, QAddress address) {
        return county.countyId.eq(address.countyId);
    }

    public static BooleanExpression JoinCountryOnAddress(QCountry country, QAddress address) {
        return country.countryId.eq(address.countryId);
    }

    public static BooleanExpression JoinMediaRelationshipOnProperty(QMediaRelationship mediaRelationship, QProperty property) {
        return mediaRelationship.relationId.eq(property.propertyID).and(mediaRelationship.mediaRelationTypeId.eq(MediaRelationType.PROPERTY)).and(mediaRelationship.isActive.eq(true)).and(mediaRelationship.isDefault.eq(true));
    }

    public static BooleanExpression JoinMediaOnMediaRelationship(QMedia media, QMediaRelationship mediaRelationship) {
        return media.mediaId.eq(mediaRelationship.mediaId);
    }

    public static BooleanExpression JoinEntityModelOnPropertyModifiedBy(QEntityModel entityModel, QProperty property) {
        return entityModel.entityId.eq(property.modifiedBy.entityId);
    }

    public static BooleanExpression JoinEntityModelOnPropertyCreatedBy(QEntityModel entityModel, QProperty property) {
        return entityModel.entityId.eq(property.CreatedBy.entityId);
    }

    public static BooleanExpression JoinEntityModelOnPropertyLastReviewedBy(QEntityModel entityModel, QProperty property) {
        return entityModel.entityId.eq(property.propertyDetails.lastReviewedBy);
    }

    public static BooleanExpression JoinPersonOnEntityModel(QPerson person,QEntityModel entityModel) {
        return person.personId.eq(entityModel.personId);
    }

    public static BooleanExpression JoinPropertyStrataRelationshipOnProperty(QPropertyStrataRelationship propertyStrataRelationship,QProperty property) {
        return propertyStrataRelationship.strataPropertyId.eq(property.propertyID).and(propertyStrataRelationship.isActive.eq(true));
    }

    public static BooleanExpression JoinPropertyFeaturesOnProperty(QPropertyFeature propertyFeature, QProperty property) {
        return propertyFeature.propertyId.eq(property.propertyID).and(propertyFeature.isActive.eq(true));
    }

    public static BooleanExpression JoinPropertyAuditLogOnProperty(QPropertyAuditLog propertyAuditLog,QProperty property) {
        return propertyAuditLog.property.propertyID.eq(property.propertyID).and(propertyAuditLog.isActive.eq(true));
    }

    public static BooleanExpression JoinStatusDefinationOnPropertyAuditLog(QStatusDefination statusDefination,QPropertyAuditLog propertyAuditLog) {
        return statusDefination.statusDefinationID.eq(propertyAuditLog.statusDefination.statusDefinationID);
    }

    public static BooleanExpression JoinUseTypeOnProperty(QUseType useType, QProperty property) {
        return useType.useTypeId.eq(property.propertyDetails.useTypeID);
    }
}
