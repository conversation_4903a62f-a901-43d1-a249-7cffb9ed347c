package com.arealytics.core.mapper;

import org.mapstruct.*;

import com.arealytics.core.domain.empiricalProd.Location;
import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.dto.request.PropertyDTO;
import com.arealytics.core.dto.response.PropertyResponseDTO;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface PropertyMapper {

    PropertyResponseDTO toDto(Property property);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(source = "propertyID", target = "propertyID")
    @Mapping(source = "propertyName", target = "propertyName")
    @Mapping(source = "parentPropertyID", target = "parentPropertyID")
    @Mapping(source = "isActive", target = "isActive")
    // PropertyDetails
    @Mapping(source = "yearBuilt", target = "propertyDetails.yearBuilt")
    @Mapping(source = "yearRenovated", target = "propertyDetails.yearRenovated")
    @Mapping(source = "floors", target = "propertyDetails.floors")
    @Mapping(source = "constructionStatusID", target = "propertyDetails.constructionStatusID")
    @Mapping(source = "constructionTypeID", target = "propertyDetails.constructionTypeID")
    @Mapping(source = "hvacTypeID", target = "propertyDetails.hvacTypeID")
    @Mapping(source = "sprinklerTypeID", target = "propertyDetails.sprinklerTypeID")
    @Mapping(source = "condoTypeID", target = "propertyDetails.condoTypeID")
    @Mapping(source = "useTypeID", target = "propertyDetails.useTypeID")
    @Mapping(source = "useTypeName", target = "propertyDetails.useTypeName")
    @Mapping(source = "specificUseID", target = "propertyDetails.specificUseID")
    @Mapping(source = "specificUseName", target = "propertyDetails.specificUseName")
    @Mapping(source = "isADAAccessible", target = "propertyDetails.isADAAccessible")
    @Mapping(source = "isVented", target = "propertyDetails.isVented")
    @Mapping(source = "isOwnerOccupied", target = "propertyDetails.isOwnerOccupied")
    @Mapping(source = "tenancyTypeID", target = "propertyDetails.tenancyTypeID")
    @Mapping(source = "isEnergyStar", target = "propertyDetails.isEnergyStar")
    @Mapping(source = "classTypeID", target = "propertyDetails.classTypeID")
    @Mapping(source = "mixedUseAllocation", target = "propertyDetails.mixedUseAllocation")
    @Mapping(source = "buildingWebsite", target = "propertyDetails.buildingWebsite")
    @Mapping(source = "buildingComments", target = "propertyDetails.buildingComments")
    @Mapping(source = "governmentInterestID", target = "propertyDetails.governmentInterestID")
    @Mapping(source = "noOfOfficeFloors", target = "propertyDetails.noOfOfficeFloors")
    @Mapping(source = "craneServed", target = "propertyDetails.craneServed")
    @Mapping(source = "roofTypeID", target = "propertyDetails.roofTypeID")
    @Mapping(source = "hasSolar", target = "propertyDetails.hasSolar")
    @Mapping(source = "trafficCount", target = "propertyDetails.trafficCount")
    @Mapping(source = "earthquakeZoneID", target = "propertyDetails.earthquakeZoneID")
    @Mapping(source = "deletedAt", target = "propertyDetails.deletedAt")
    @Mapping(source = "energyStarRatingID", target = "propertyDetails.energyStarRatingID")
    @Mapping(source = "waterStarRatingID", target = "propertyDetails.waterStarRatingID")
    @Mapping(source = "greenStarRatingID", target = "propertyDetails.greenStarRatingID")
    @Mapping(source = "occupiedPercentage", target = "propertyDetails.occupiedPercentage")
    @Mapping(
            source = "directOccupiedPercentage",
            target = "propertyDetails.directOccupiedPercentage")
    @Mapping(source = "currentTitle", target = "propertyDetails.currentTitle")
    @Mapping(source = "tiAllowance", target = "propertyDetails.tiAllowance")
    @Mapping(source = "gresbScore", target = "propertyDetails.gresbScore")
    @Mapping(source = "gresbScoreMin", target = "propertyDetails.gresbScoreMin")
    @Mapping(source = "gresbScoreMax", target = "propertyDetails.gresbScoreMax")
    @Mapping(source = "actualCompletion", target = "propertyDetails.actualCompletion")
    @Mapping(source = "titleReferenceDate", target = "propertyDetails.titleReferenceDate")
    @Mapping(source = "landUse", target = "propertyDetails.landUse")
    @Mapping(source = "lastReviewedBy", target = "propertyDetails.lastReviewedBy")
    @Mapping(source = "lastReviewedDate", target = "propertyDetails.lastReviewedDate")
    @Mapping(source = "constructionStartDate", target = "propertyDetails.constructionStartDate")
    @Mapping(source = "estCompletion", target = "propertyDetails.estCompletion")
    @Mapping(source = "estimatedCompletionDate", target = "propertyDetails.estimatedCompletionDate")
    @Mapping(source = "bookValue", target = "propertyDetails.bookValue")
    @Mapping(source = "bookValueDate", target = "propertyDetails.bookValueDate")
    @Mapping(source = "minAskingSalePrice", target = "propertyDetails.minAskingSalePrice")
    @Mapping(source = "maxAskingSalePrice", target = "propertyDetails.maxAskingSalePrice")
    @Mapping(
            source = "askingLeaseRatePerYearMin",
            target = "propertyDetails.askingLeaseRatePerYearMin")
    @Mapping(
            source = "askingLeaseRatePerYearMax",
            target = "propertyDetails.askingLeaseRatePerYearMax")
    @Mapping(source = "parcelInfo", target = "propertyDetails.parcelInfo")
    @Mapping(source = "propertyKey", target = "propertyDetails.propertyKey")
    @Mapping(source = "mainPhotoUrl", target = "propertyDetails.mainPhotoUrl")
    @Mapping(source = "listingCompany", target = "propertyDetails.listingCompany")
    // PropertyLocation
    @Mapping(source = "metroId", target = "location.metroId")
    @Mapping(source = "marketId", target = "location.marketId")
    @Mapping(source = "parkComplexName", target = "location.parkComplexName")
    @Mapping(source = "address", target = "location.address")
    @Mapping(source = "zoning", target = "location.zoning")
    @Mapping(source = "zoningClassID", target = "location.zoningClassID")
    @Mapping(source = "zoningCode", target = "location.zoningCode")
    @Mapping(source = "lga", target = "location.lga")
    @Mapping(source = "geoscapePropertyID", target = "location.geoscapePropertyID")
    @Mapping(source = "counsilTaxID", target = "location.counsilTaxID")
    @Mapping(source = "valuerGeneralID", target = "location.valuerGeneralID")
    @Mapping(source = "surroundingLandUse", target = "location.surroundingLandUse")
    @Mapping(source = "legalDescription", target = "location.legalDescription")
    @Mapping(source = "useAddressAsPropertyName", target = "location.useAddressAsPropertyName")
    // PropertyAmenities
    @Mapping(source = "amenities", target = "amenities.amenities")
    @Mapping(source = "parkingSpaces", target = "amenities.parkingSpaces")
    @Mapping(source = "parkingRatio", target = "amenities.parkingRatio")
    @Mapping(source = "passengerElevators", target = "amenities.passengerElevators")
    @Mapping(source = "parkingElevators", target = "amenities.parkingElevators")
    @Mapping(source = "freighElevators", target = "amenities.freighElevators")
    @Mapping(source = "dockHigh", target = "amenities.dockHigh")
    @Mapping(source = "gradeLevelIn", target = "amenities.gradeLevelIn")
    @Mapping(source = "truckWell", target = "amenities.truckWell")
    @Mapping(source = "phase", target = "amenities.phase")
    @Mapping(source = "volts", target = "amenities.volts")
    @Mapping(source = "amps", target = "amenities.amps")
    @Mapping(source = "powerComments", target = "amenities.powerComments")
    @Mapping(source = "bayWidth", target = "amenities.bayWidth")
    @Mapping(source = "bayDepth", target = "amenities.bayDepth")
    @Mapping(source = "includeInAnalytics", target = "amenities.includeInAnalytics")
    @Mapping(source = "officeAC", target = "amenities.officeAC")
    @Mapping(source = "potentialZoningID", target = "amenities.potentialZoningID")
    @Mapping(source = "railServed", target = "amenities.railServed")
    @Mapping(source = "isFloodPlain", target = "amenities.isFloodPlain")
    @Mapping(source = "buildSpecStatusID", target = "amenities.buildSpecStatusID")
    @Mapping(source = "hasYardFenced", target = "amenities.hasYardFenced")
    @Mapping(source = "hasYardUnfenced", target = "amenities.hasYardUnfenced")
    @Mapping(source = "yardPaved", target = "amenities.yardPaved")
    @Mapping(source = "hasResCoveredParking", target = "amenities.hasResCoveredParking")
    @Mapping(source = "reservedParkingSpaces", target = "amenities.reservedParkingSpaces")
    @Mapping(
            source = "reservedParkingSpacesRatePerMonth",
            target = "amenities.reservedParkingSpacesRatePerMonth")
    @Mapping(source = "hasReservedParkingSpaces", target = "amenities.hasReservedParkingSpaces")
    @Mapping(source = "depth", target = "amenities.depth")
    @Mapping(source = "width", target = "amenities.width")
    @Mapping(source = "internalComments", target = "amenities.internalComments")
    @Mapping(source = "noOfAnchor", target = "amenities.noOfAnchor")
    @Mapping(source = "adding", target = "amenities.adding")
    @Mapping(source = "hasSprinkler", target = "amenities.hasSprinkler")
    @Mapping(source = "hasPortAccess", target = "amenities.hasPortAccess")
    @Mapping(source = "hasYard", target = "amenities.hasYard")
    @Mapping(source = "unreservedParkingSpaces", target = "amenities.unreservedParkingSpaces")
    @Mapping(
            source = "unreservedParkingSpacesRatePerMonth",
            target = "amenities.unreservedParkingSpacesRatePerMonth")
    @Mapping(source = "hasUnreservedParkingSpaces", target = "amenities.hasUnreservedParkingSpaces")
    @Mapping(source = "propertyComments", target = "amenities.propertyComments")
    @Mapping(source = "utilityComments", target = "amenities.utilityComments")
    @Mapping(source = "noOfUnits", target = "amenities.noOfUnits")
    @Mapping(source = "totalAnchor", target = "amenities.totalAnchor")
    @Mapping(source = "hvac", target = "amenities.hvac")
    @Mapping(source = "lifts", target = "amenities.lifts")
    @Mapping(source = "liftsCount", target = "amenities.liftsCount")
    @Mapping(source = "powerType", target = "amenities.powerType")
    @Mapping(source = "vacancy", target = "amenities.vacancy")
    @Mapping(source = "LCount", target = "amenities.LCount")
    @Mapping(source = "llCount", target = "amenities.llCount")
    @Mapping(source = "dlCount", target = "amenities.dlCount")
    @Mapping(source = "slCount", target = "amenities.slCount")
    // PropertySize
    @Mapping(source = "sizeSourceID", target = "propertySize.sizeSourceID")
    @Mapping(source = "clearHeightMin", target = "propertySize.clearHeightMin")
    @Mapping(source = "clearHeightMax", target = "propertySize.clearHeightMax")
    @Mapping(source = "retailFrontage", target = "propertySize.retailFrontage")
    @Mapping(source = "typicalFloorSize", target = "propertySize.typicalFloorSize")
    @Mapping(source = "hardstandArea", target = "propertySize.hardstandArea")
    @Mapping(source = "hardstandAreaSourceID", target = "propertySize.hardstandAreaSourceID")
    @Mapping(
            source = "contributedGBASizeSourceID",
            target = "propertySize.contributedGBASizeSourceID")
    @Mapping(source = "glasSizeSourceID", target = "propertySize.glasSizeSourceID")
    @Mapping(source = "glarSizeSourceID", target = "propertySize.glarSizeSourceID")
    @Mapping(source = "mezzanine", target = "propertySize.mezzanine")
    @Mapping(source = "awnings", target = "propertySize.awnings")
    @Mapping(source = "awningsCount", target = "propertySize.awningsCount")
    @Mapping(source = "buildingSize", target = "propertySize.buildingSize")
    @Mapping(source = "lotSize", target = "propertySize.lotSize")
    @Mapping(source = "minFloorSize", target = "propertySize.minFloorSize")
    @Mapping(source = "maxFloorSize", target = "propertySize.maxFloorSize")
    @Mapping(source = "officeSize", target = "propertySize.officeSize")
    @Mapping(source = "retailSize", target = "propertySize.retailSize")
    @Mapping(source = "totalAvailableSF", target = "propertySize.totalAvailableSF")
    @Mapping(source = "buildingSizeSF", target = "propertySize.buildingSizeSF")
    @Mapping(source = "lotSizeSF", target = "propertySize.lotSizeSF")
    @Mapping(source = "totalSaleSizeSF", target = "propertySize.totalSaleSizeSF")
    @Mapping(source = "contributedGBASizeSF", target = "propertySize.contributedGBASizeSF")
    @Mapping(source = "glaSizeSF", target = "propertySize.glaSizeSF")
    @Mapping(source = "glarSizeSF", target = "propertySize.glarSizeSF")
    @Mapping(source = "mezzanineSizeSF", target = "propertySize.mezzanineSizeSF")
    @Mapping(source = "awningsSizeSF", target = "propertySize.awningsSizeSF")
    @Mapping(source = "lotSizeAC", target = "propertySize.lotSizeAC")
    @Mapping(source = "GBASizeSource", target = "propertySize.GBASizeSource")
    @Mapping(source = "lotSizeSourceID", target = "propertySize.lotSizeSourceID")
    @Mapping(source = "NRASizeSourceID", target = "propertySize.NRASizeSourceID")
    @Mapping(source = "smallestFloor", target = "propertySize.smallestFloor")
    @Mapping(source = "largestFloor", target = "propertySize.largestFloor")
    @Mapping(source = "nla", target = "propertySize.nla")
    @Mapping(source = "nlaac", target = "propertySize.nlaac")
    @Mapping(source = "contributedGBA", target = "propertySize.contributedGBA")
    @Mapping(source = "gla", target = "propertySize.gla")
    @Mapping(source = "glar", target = "propertySize.glar")
    // PropertyInternalToolFields
    @Mapping(source = "researchTypeID", target = "internalToolFields.researchTypeID")
    @Mapping(source = "researchTypeName", target = "internalToolFields.researchTypeName")
    @Mapping(source = "trueOwners", target = "internalToolFields.trueOwners")
    @Mapping(source = "recordedOwners", target = "internalToolFields.recordedOwners")
    @Mapping(source = "isSkipped", target = "internalToolFields.isSkipped")
    @Mapping(
            source = "isMultiplePolygonsNeeded",
            target = "internalToolFields.isMultiplePolygonsNeeded")
    @Mapping(source = "needsResearchComments", target = "internalToolFields.needsResearchComments")
    @Mapping(target = "modifiedBy", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    Property toEntity(PropertyDTO dto, @MappingTarget Property entity);

    // Map PropertyDTO to Location
    @Mapping(source = "latitude", target = "latitude")
    @Mapping(source = "longitude", target = "longitude")
    @Mapping(source = "ZCoordinate", target = "zCoordinate")
    @Mapping(source = "gisShapeID", target = "gisShapeID")
    @Mapping(source = "rooftopSourceID", target = "rooftopSourceID")
    Location toLocationEntity(PropertyDTO propertyDTO);
}
