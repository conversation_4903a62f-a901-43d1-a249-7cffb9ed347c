package com.arealytics.core.mapper;

import org.mapstruct.*;

import com.arealytics.core.domain.empiricalProd.Address;
import com.arealytics.core.dto.request.PropertyDTO;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface AddressMapper {

    // Mapping for creating a new Address
    @Mapping(target = "addressTypeId", constant = "PHYSICAL")
    @Mapping(target = "parentId", source = "propertyId")
    @Mapping(target = "parentTableId", constant = "Property")
    @Mapping(target = "locationId", source = "locationId")
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "address1", source = "propertyDTO.address1")
    @Mapping(target = "address2", source = "propertyDTO.address2")
    @Mapping(target = "zipCode", source = "propertyDTO.zipCode")
    @Mapping(target = "zip4", source = "propertyDTO.zip4")
    @Mapping(target = "stateId", source = "propertyDTO.stateID")
    @Mapping(target = "cityId", source = "propertyDTO.cityID")
    @Mapping(target = "countyId", source = "propertyDTO.countyID")
    @Mapping(target = "countryId", source = "propertyDTO.countryId")
    @Mapping(target = "quadrantId", source = "propertyDTO.quadrantID")
    @Mapping(target = "marketStateId", source = "propertyDTO.stateID")
    @Mapping(target = "floorNumber", source = "propertyDTO.floorNumber")
    @Mapping(target = "buildingNumber", source = "propertyDTO.buildingNumber")
    @Mapping(target = "partOfCenterComplex", source = "propertyDTO.partOfCenterComplex")
    @Mapping(target = "complexName", source = "propertyDTO.complexName")
    @Mapping(target = "primaryStreet", source = "propertyDTO.primaryStreet")
    @Mapping(target = "secondaryStreet", source = "propertyDTO.secondaryStreet")
    @Mapping(target = "eastWestSt", source = "propertyDTO.eastWestSt")
    @Mapping(target = "northSouthSt", source = "propertyDTO.northSouthSt")
    @Mapping(target = "addressStreetName", source = "propertyDTO.addressStreetName")
    @Mapping(target = "primaryAccess", source = "propertyDTO.primaryAccess")
    @Mapping(target = "primaryTrafficCount", source = "propertyDTO.primaryTrafficCount")
    @Mapping(target = "primaryTrafficCountDate", source = "propertyDTO.primaryTrafficCountDate")
    @Mapping(target = "primaryFrontage", source = "propertyDTO.primaryFrontage")
    @Mapping(target = "secondaryAccess", source = "propertyDTO.secondaryAccess")
    @Mapping(target = "secondaryTrafficCount", source = "propertyDTO.secondaryTrafficCount")
    @Mapping(target = "secondaryTrafficCountDate", source = "propertyDTO.secondaryTrafficCountDate")
    @Mapping(target = "secondaryFrontage", source = "propertyDTO.secondaryFrontage")
    @Mapping(target = "sequence", source = "propertyDTO.sequence")
    @Mapping(target = "isIntersection", source = "propertyDTO.isIntersection")
    @Mapping(target = "streetNumberMin", source = "propertyDTO.streetNumberMin")
    @Mapping(target = "streetNumberMax", source = "propertyDTO.streetNumberMax")
    @Mapping(
            target = "addressStreetNumber",
            source = "propertyDTO",
            qualifiedByName = "buildStreetNumber")
    @Mapping(target = "addressText", source = "propertyDTO", qualifiedByName = "buildAddressText")
    @Mapping(
            target = "streetNumberMinN",
            source = "propertyDTO.streetNumberMin",
            qualifiedByName = "toNumericStreetNumber")
    @Mapping(
            target = "streetNumberMaxN",
            source = "propertyDTO.streetNumberMax",
            qualifiedByName = "toNumericStreetNumber")
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "modifiedBy", ignore = true)
    Address toAddress(PropertyDTO propertyDTO, Integer locationId, Integer propertyId);

    // Mapping for updating an existing Address
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "address1", source = "address1")
    @Mapping(target = "address2", source = "address2")
    @Mapping(target = "zipCode", source = "zipCode")
    @Mapping(target = "zip4", source = "zip4")
    @Mapping(target = "stateId", source = "stateID")
    @Mapping(target = "cityId", source = "cityID")
    @Mapping(target = "countyId", source = "countyID")
    @Mapping(target = "countryId", source = "countryId")
    @Mapping(target = "quadrantId", source = "quadrantID")
    @Mapping(target = "marketStateId", source = "stateID")
    @Mapping(target = "floorNumber", source = "floorNumber")
    @Mapping(target = "buildingNumber", source = "buildingNumber")
    @Mapping(target = "partOfCenterComplex", source = "partOfCenterComplex")
    @Mapping(target = "complexName", source = "complexName")
    @Mapping(target = "primaryStreet", source = "primaryStreet")
    @Mapping(target = "secondaryStreet", source = "secondaryStreet")
    @Mapping(target = "eastWestSt", source = "eastWestSt")
    @Mapping(target = "northSouthSt", source = "northSouthSt")
    @Mapping(target = "addressStreetName", source = "addressStreetName")
    @Mapping(target = "primaryAccess", source = "primaryAccess")
    @Mapping(target = "primaryTrafficCount", source = "primaryTrafficCount")
    @Mapping(target = "primaryTrafficCountDate", source = "primaryTrafficCountDate")
    @Mapping(target = "primaryFrontage", source = "primaryFrontage")
    @Mapping(target = "secondaryAccess", source = "secondaryAccess")
    @Mapping(target = "secondaryTrafficCount", source = "secondaryTrafficCount")
    @Mapping(target = "secondaryTrafficCountDate", source = "secondaryTrafficCountDate")
    @Mapping(target = "secondaryFrontage", source = "secondaryFrontage")
    @Mapping(target = "sequence", source = "sequence")
    @Mapping(target = "isIntersection", source = "isIntersection")
    @Mapping(target = "streetNumberMin", source = "streetNumberMin")
    @Mapping(target = "streetNumberMax", source = "streetNumberMax")
    @Mapping(
            target = "addressStreetNumber",
            source = "propertyDTO",
            qualifiedByName = "buildStreetNumber")
    @Mapping(target = "addressText", source = "propertyDTO", qualifiedByName = "buildAddressText")
    @Mapping(
            target = "streetNumberMinN",
            source = "streetNumberMin",
            qualifiedByName = "toNumericStreetNumber")
    @Mapping(
            target = "streetNumberMaxN",
            source = "streetNumberMax",
            qualifiedByName = "toNumericStreetNumber")
     @Mapping(target = "createdBy", ignore = true)
     @Mapping(target = "modifiedBy", ignore = true)
    Address updateAddressFromDto(PropertyDTO propertyDTO, @MappingTarget Address address);

    @Named("buildStreetNumber")
    default String buildStreetNumber(PropertyDTO dto) {
        String min = dto.getStreetNumberMin();
        String max = dto.getStreetNumberMax();
        if (min != null && (max == null || min.equals(max))) {
            return min;
        } else if (min != null && max != null && !max.isEmpty()) {
            return min + "-" + max;
        }
        return min;
    }

    @Named("buildAddressText")
    default String buildAddressText(PropertyDTO dto) {
        String streetNumber = buildStreetNumber(dto);
        String addressText =
                (streetNumber != null ? streetNumber : "")
                        + (dto.getAddressStreetName() != null
                                ? " " + dto.getAddressStreetName()
                                : "");
        return addressText.trim();
    }

    @Named("toNumericStreetNumber")
    default Integer toNumericStreetNumber(String streetNumber) {
        if (streetNumber == null) {
            return null;
        }
        try {
            return Integer.parseUnsignedInt(streetNumber);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * Maps a Boolean value to an Integer (1 for true, 0 for false)
     */
    default Integer map(Boolean value) {
        return value == null ? null : (value ? 1 : 0);
    }
}
