package com.arealytics.core.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueMappingStrategy;

import com.arealytics.core.domain.empiricalProd.MediaRelationship;
import com.arealytics.core.dto.response.MediaDTO;

@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL)
public interface MediaMapper {

  @Mapping(source = "media.mediaId", target = "mediaId")
  @Mapping(source = "media.mediaName", target = "mediaName")
  @Mapping(source = "media.height", target = "height")
  @Mapping(source = "media.width", target = "width")
  @Mapping(source = "media.size", target = "size")
  @Mapping(source = "media.path", target = "path")
  @Mapping(source = "media.ext", target = "ext")
  @Mapping(source = "media.description", target = "description")
  @Mapping(target = "modifiedByName", ignore = true)
  @Mapping(source = "mediaRelationshipId", target = "mediaRelationshipId")
  @Mapping(source = "mediaRelationTypeId", target = "mediaRelationTypeId")
  @Mapping(source = "mediaTypeId", target = "mediaTypeId")
  @Mapping(source = "mediaSubTypeId", target = "mediaSubTypeId")
  @Mapping(source = "relationId", target = "relationId")
  @Mapping(source = "isDefault", target = "isDefault")
  @Mapping(source = "media.isOwnMedia", target = "isOwnMedia")
  @Mapping(source = "media.mediaSourceId", target = "mediaSourceId")
  @Mapping(source = "media.sourceComments", target = "sourceComments")
  @Mapping(source = "media.createdBy.entityId", target = "createdBy")
  @Mapping(source = "media.createdDate", target = "createdDate")
  @Mapping(source = "media.modifiedBy.entityId", target = "modifiedBy")
  @Mapping(source = "media.modifiedDate", target = "modifiedDate")
  @Mapping(target = "hasEdit", ignore = true)
  @Mapping(target = "buildingSizeSF", ignore = true)
  MediaDTO toDTO(MediaRelationship mediaRelationship);
}
