package com.arealytics.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.servers.Server;

import java.util.List;

@Configuration
public class SwaggerConfig {
    @Value("${swagger.server.url}")
    private String serverUrl;

    @Value("${swagger.server.description}")
    private String serverDescription;

    @Bean
    public OpenAPI customOpenAPI() {
        Server activeServer = new Server().url(serverUrl).description(serverDescription);
        return new OpenAPI()
                .info(
                        new Info()
                                .title("Phoenix API Documentation")
                                .version("1.0")
                                .description("API documentation for the Phoenix Application")).servers(List.of(activeServer));
    }
}
