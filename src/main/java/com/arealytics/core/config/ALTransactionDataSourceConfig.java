package com.arealytics.core.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import jakarta.persistence.EntityManagerFactory;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.arealytics.core.repository.empiricalProd",
        entityManagerFactoryRef = "empiricalProdEntityManagerFactory",
        transactionManagerRef = "empiricalProdTransactionManager")
public class ALTransactionDataSourceConfig {
    @Primary
    @Bean(name = "empiricalProdDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.empirical-prod")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Primary
    @Bean(name = "empiricalProdEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("empiricalProdDataSource") DataSource dataSource) {
        return builder.dataSource(dataSource)
                .packages(
                        "com.arealytics.core.domain.empiricalProd",
                        "com.arealytics.core.domain.shared") // Entities for this DB
                .persistenceUnit("empiricalProdPU")
                .build();
    }

    @Primary
    @Bean(name = "empiricalProdTransactionManager")
    public PlatformTransactionManager transactionManager(
            @Qualifier("empiricalProdEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
