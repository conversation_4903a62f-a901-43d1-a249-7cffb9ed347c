package com.arealytics.core.config;

import javax.sql.DataSource;

import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FlywayConfig {

    @Value("${flyway.transaction.location}")
    private String transactionMigrationLocation;

    @Value("${flyway.gis.location}")
    private String gisMigrationLocation;

    private static final String BASELINE_VERSION = "2";


    private Flyway configureFlyway(DataSource dataSource, String location) {
        return Flyway.configure()
                .dataSource(dataSource)
                .locations(location)
                .validateMigrationNaming(true)
                .baselineOnMigrate(true)
                .baselineVersion(BASELINE_VERSION)
                .load();
    }

    @Bean(name = "transactionSchemaFlyway")
    public Flyway transactionSchemaFlyway(
            @Qualifier("empiricalProdDataSource") DataSource dataSource) {
        return configureFlyway(dataSource, transactionMigrationLocation);
    }

    @Bean(name = "gisSchemaFlyway")
    public Flyway gisSchemaFlyway(@Qualifier("empiricalGISDataSource") DataSource dataSource) {
        return configureFlyway(dataSource, gisMigrationLocation);
    }

    @Bean
    public FlywayMigrationInitializer transactionFlywayInitializer(
            @Qualifier("transactionSchemaFlyway") Flyway flyway) {
        return new FlywayMigrationInitializer(flyway, null);
    }

    @Bean
    public FlywayMigrationInitializer gisFlywayInitializer(
            @Qualifier("gisSchemaFlyway") Flyway flyway) {
        return new FlywayMigrationInitializer(flyway, null);
    }
}
