package com.arealytics.core.config;

import org.hibernate.envers.RevisionListener;

import com.arealytics.core.domain.shared.CustomRevisionEntity;

public class CustomRevisionListener implements RevisionListener {
    @Override
    public void newRevision(Object revisionEntity) {
        CustomRevisionEntity revision = (CustomRevisionEntity) revisionEntity;
        // TODO : Need to extract the application Id and entityId from token
        revision.setChangedBy(22);

        revision.setApplicationId(1);
    }
}
