package com.arealytics.core.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import jakarta.persistence.EntityManagerFactory;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.arealytics.core.repository.empiricalGIS",
        entityManagerFactoryRef = "empiricalGISEntityManagerFactory",
        transactionManagerRef = "empiricalGISTransactionManager")
public class ALGISDataSourceConfig {
    @Bean(name = "empiricalGISDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.empirical-gis")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "empiricalGISEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("empiricalGISDataSource") DataSource dataSource) {
        return builder.dataSource(dataSource)
                .packages(
                        "com.arealytics.core.domain.empiricalGIS",
                        "com.arealytics.core.domain.shared") // Entities for this DB
                .persistenceUnit("empiricalGISPU")
                .build();
    }

    @Bean(name = "empiricalGISTransactionManager")
    public PlatformTransactionManager transactionManager(
            @Qualifier("empiricalGISEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
