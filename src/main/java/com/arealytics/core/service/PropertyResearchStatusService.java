package com.arealytics.core.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.domain.empiricalProd.PropertyResearchStatus;
import com.arealytics.core.repository.empiricalProd.PropertyResearchStatusRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class PropertyResearchStatusService {

    private final PropertyResearchStatusRepository propertyResearchStatusRepository;

    /**
     * Creates a default PropertyResearchStatus for a given property.
     *
     * @param property The property entity to associate with the research status
     * @return The saved PropertyResearchStatus entity
     */
    @Transactional
    public PropertyResearchStatus createDefaultResearchStatus(Property property) {
        PropertyResearchStatus researchStatus = new PropertyResearchStatus();
        researchStatus.setProperty(property);
        researchStatus.setPropertyResearchTypeId(1);
        researchStatus.setIsActive(true);
        return propertyResearchStatusRepository.save(researchStatus);
    }
}
