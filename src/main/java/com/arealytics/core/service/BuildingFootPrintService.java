package com.arealytics.core.service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.core.constants.CacheNames;
import com.arealytics.core.domain.empiricalGIS.BuildingFootPrint;
import com.arealytics.core.dto.request.BuildingFootPrintRequestDTO;
import com.arealytics.core.dto.request.BuildingFootprintCollectionRequestDTO;
import com.arealytics.core.dto.response.BuildingFootPrintDTO;
import com.arealytics.core.exception.ResourceNotFoundException;
import com.arealytics.core.mapper.BuildingFootPrintMapper;
import com.arealytics.core.repository.empiricalGIS.BuildingFootPrintRepository;
import com.arealytics.core.utils.PartialUpdateUtils;
import com.arealytics.core.utils.UnitConversionUtil;

@Service
public class BuildingFootPrintService {
    private final BuildingFootPrintRepository buildingFootprintRepo;
    private final BuildingFootPrintMapper mapper;

    @Autowired
    public BuildingFootPrintService(
            BuildingFootPrintRepository buildingFootprintRepo, BuildingFootPrintMapper mapper) {
        this.buildingFootprintRepo = buildingFootprintRepo;
        this.mapper = mapper;
    }

    @Transactional("empiricalGISTransactionManager")
    @Cacheable(value = CacheNames.BUILDING_FOOTPRINTS, key = "#propertyId")
    public List<BuildingFootPrintDTO> getBuildingFootprintsByPropertyId(Integer propertyId)
            throws Exception {
                return getBuildingFootPrintDTOs(propertyId);
    }

    private BuildingFootPrintDTO enhanceDTO(BuildingFootPrint buildingFootprint) {
        BuildingFootPrintDTO dto = mapper.toDTO(buildingFootprint);
        if (buildingFootprint.getShape() != null) {
            dto.setBuildingFootPrint(new WKTWriter().write(buildingFootprint.getShape()));
        }

        if (buildingFootprint.getSizeInSF() != null) {
            dto.setSizeInSM(UnitConversionUtil.sqftToSqm(buildingFootprint.getSizeInSF()));
        }
        return dto;
    }

    @Transactional("empiricalGISTransactionManager")
    @CachePut(value = CacheNames.BUILDING_FOOTPRINTS, key = "#request.propertyId")
    public List<BuildingFootPrintDTO> saveBuildingFootprints(
            BuildingFootprintCollectionRequestDTO request) throws Exception {

        if (request == null) {
            throw new IllegalArgumentException("Request body cannot be null or empty.");
        }

        if (request.getPropertyId() == null) {
            throw new IllegalArgumentException("Property ID is required.");
        }

        List<BuildingFootPrintRequestDTO> buildingFootprintRequests =
                request.getBuildingFootPrintRequestDTOS();
        if (buildingFootprintRequests == null || buildingFootprintRequests.isEmpty()) {
            throw new IllegalArgumentException("At least one building footprint must be provided.");
        }

        Integer propertyId = request.getPropertyId();

        for (BuildingFootPrintRequestDTO footprintRequest : buildingFootprintRequests) {
            BuildingFootPrint buildingFootPrint;
            boolean isNewFootprint =
                    footprintRequest.getBuildingFootPrintId() == null
                            || footprintRequest.getBuildingFootPrintId() <= 0;

            if (isNewFootprint) {
                buildingFootPrint = mapper.toEntity(footprintRequest);
            } else {
                List<BuildingFootPrint> existingBuildingFootPrints =
                        buildingFootprintRepo.findByBuildingFootPrintId(
                                footprintRequest.getBuildingFootPrintId());

                if (existingBuildingFootPrints.isEmpty()) {
                    throw new IllegalArgumentException(
                            "No building footprint found with id: "
                                    + footprintRequest.getBuildingFootPrintId());
                }
                buildingFootPrint = existingBuildingFootPrints.getFirst();
                BuildingFootPrint updateBuildingFootPrint = mapper.toEntity(footprintRequest);
                PartialUpdateUtils.copyNonNullProperties(
                        updateBuildingFootPrint, buildingFootPrint);
                buildingFootPrint.setModifiedDate(Instant.now());
            }
            buildingFootPrint.setCrePropertyId(propertyId);
            buildingFootPrint.setIsActive(true);
            if (footprintRequest.getArea() != null) {
                buildingFootPrint.setSizeInSF(
                        UnitConversionUtil.sqmToSqft(footprintRequest.getArea()));
            }

            if (footprintRequest.getMinFloorNumber() != null
                    && footprintRequest.getMaxFloorNumber() != null) {
                try {
                    int min = footprintRequest.getMinFloorNumber();
                    int max = footprintRequest.getMaxFloorNumber();
                    buildingFootPrint.setFloors(max - min + 1);
                } catch (NumberFormatException ex) {
                    throw new NumberFormatException("Invalid floor number input.");
                }
            }

            if (footprintRequest.getBuildingFootPrint() != null) {
                try {
                    WKTReader reader = new WKTReader();
                    buildingFootPrint.setShape(
                            reader.read(footprintRequest.getBuildingFootPrint()));
                } catch (Exception ex) {
                    throw new IllegalArgumentException("Invalid WKT geometry: " + ex.getMessage());
                }
            }

            BuildingFootPrint savedEntity = buildingFootprintRepo.save(buildingFootPrint);
            List<BuildingFootPrintDTO> newFootprints = new ArrayList<>();
            newFootprints.add(enhanceDTO(savedEntity));
        }
        return getBuildingFootPrintDTOs(propertyId);
    }

    @Transactional("empiricalGISTransactionManager")
    @CacheEvict(value = CacheNames.BUILDING_FOOTPRINTS, key = "#propertyId")
    public void deleteBuildingFootprint(String buildingFootPrintIds, Integer propertyId) {

        List<BuildingFootPrint> propertyExists =
                buildingFootprintRepo.findByCrePropertyId(propertyId);
        if (propertyExists.isEmpty()) {
            throw new IllegalArgumentException(
                    "No property found for the given Property ID: " + propertyId);
        }

        if (buildingFootPrintIds == null || buildingFootPrintIds.isBlank()) {
            throw new IllegalArgumentException("BuildingFootPrintIds cannot be null or empty");
        }

        List<Integer> ids =
                Arrays.stream(buildingFootPrintIds.split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Integer::parseInt)
                        .distinct()
                        .toList();

        List<BuildingFootPrint> matchingFootprints =
                buildingFootprintRepo.findByBuildingFootPrintIdInAndCrePropertyId(ids, propertyId);
        if (matchingFootprints.isEmpty()) {
            throw new ResourceNotFoundException(
                    "No matching building footprints found for the given property");
        }
        for (BuildingFootPrint footprint : matchingFootprints) {
            footprint.setIsActive(false);
        }

        buildingFootprintRepo.saveAll(matchingFootprints);
    }

    private List<BuildingFootPrintDTO> getBuildingFootPrintDTOs(Integer propertyId) throws Exception {
        List<BuildingFootPrint> buildingFootPrints =
                buildingFootprintRepo.findByCrePropertyIdAndIsActiveTrueOrderByIsDefaultDesc(propertyId);
        return buildingFootPrints.stream().map(this::enhanceDTO).toList();
    }
}
