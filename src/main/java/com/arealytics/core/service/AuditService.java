package com.arealytics.core.service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditEntity;
import org.javers.core.Javers;
import org.javers.core.diff.Change;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.ValueChange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.arealytics.core.constants.AuditLogMetadata;
import com.arealytics.core.domain.empiricalProd.Application;
import com.arealytics.core.domain.empiricalProd.ChangeLogFields;
import com.arealytics.core.domain.shared.CustomRevisionEntity;
import com.arealytics.core.enumeration.AuditAction;
import com.arealytics.core.enumeration.FieldDataType;
import com.arealytics.core.enumeration.ParentTable;
import com.arealytics.core.repository.empiricalProd.ApplicationRepository;
import com.arealytics.core.repository.empiricalProd.ChangeLogFieldsRepository;
import com.arealytics.core.utils.AuditLogUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;

@Service
public class AuditService {

    @Autowired
    private Javers javers;

    @Autowired
    private ChangeLogFieldsRepository changeLogFieldsRepository;

    @Autowired
    private ApplicationRepository applicationRepository;

    private final EntityManager transactionEntityManager;
    private final EntityManager GISEntityManager;

    public AuditService(
            @Qualifier("empiricalProdEntityManagerFactory") EntityManagerFactory transactionEntityManagerFactory,
            @Qualifier("empiricalGISEntityManagerFactory") EntityManagerFactory GISEntityManagerFactory) {
        this.transactionEntityManager = transactionEntityManagerFactory.createEntityManager();
        this.GISEntityManager = GISEntityManagerFactory.createEntityManager();
    }

    // Retrieves ChangeLogField details by name, status, and parent table.
    public ChangeLogFields getChangeLogFieldDetailsByName(
            String fieldName, Boolean active, ParentTable tableName) {
        Optional<ChangeLogFields> changeLogField = changeLogFieldsRepository.findByFieldNameAndIsActiveAndParentTableId(
                fieldName, active, tableName);
        return changeLogField.orElse(null);
    }

    // Retrieves Application details by application ID.
    private Application getApplicationDetailsById(Integer applicationId) {
        Optional<Application> application = applicationRepository.findById(applicationId);
        return application.orElse(null);
    }

    // Fetches audit logs for a given entity and its ID.
    public List<Map<String, Object>> fetchAuditLogsByEntityId(String entityType, Integer entityId) {
        AuditLogMetadata.EntityMetadata metadata = AuditLogMetadata.getEntityMetadata(entityType);
        Class<?> entityClass = metadata.getEntityClass();
        String idFieldName = metadata.getIdField();
        Map<String, String> changeLogFieldsMapping = metadata.getChangeLogFieldsMapping();

        if (entityClass == null) {
            throw new IllegalArgumentException("Unsupported entity type: " + entityType);
        }

        List<Object[]> revisions = fetchEntityRevisions(transactionEntityManager, entityClass, entityId);

        return formatAuditLog(
                entityId,
                ParentTable.valueOf(entityType),
                entityClass,
                revisions,
                idFieldName,
                changeLogFieldsMapping);
    }

    // Fetches the revisions of an entity.
    @SuppressWarnings("unchecked")
    private <T> List<Object[]> fetchEntityRevisions(EntityManager entityManager, Class<T> entityClass, Integer entityId) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);
        return auditReader.createQuery()
                .forRevisionsOfEntity(entityClass, false, true)
                .add(AuditEntity.id().eq(entityId))
                .getResultList();
    }

    // Formats audit logs for entity revisions.
    public <T> List<Map<String, Object>> formatAuditLog(
            Integer entityId,
            ParentTable parentTable,
            Class<T> entityClass,
            List<Object[]> revisions,
            String idFieldName,
            Map<String, String> changeLogFieldsMapping) {

        List<Map<String, Object>> changelog = new ArrayList<>();
        T previousEntity = null;

        // Iterate through revisions and compare entity changes
        for (Object[] revision : revisions) {
            @SuppressWarnings("unchecked")
            T currentEntity = (T) revision[0];
            DefaultRevisionEntity revEntity = (DefaultRevisionEntity) revision[1];
            RevisionType revisionType = (RevisionType) revision[2];
            Instant timestamp = Instant.ofEpochMilli(revEntity.getTimestamp());
            String revisionTypeDisplayText = AuditAction.mappedValue(revisionType.name());

            Integer applicationId = null;
            Integer changedBy = null;
            String applicationName = null;

            // Get metadata for the revision (application ID and user)
            if (revEntity instanceof CustomRevisionEntity) {
                CustomRevisionEntity customMetadata = (CustomRevisionEntity) revEntity;
                applicationId = customMetadata.getApplicationId();
                changedBy = customMetadata.getChangedBy();
                applicationName = getApplicationName(applicationId);
            }

            // Handle ADD and DEL operations
            if (isAddOrDel(revisionTypeDisplayText)) {
                changelog.add(createAuditLogEntryForAddDel(entityId, idFieldName, timestamp, revisionTypeDisplayText, changedBy, applicationName));
            } else if (previousEntity != null) {
                // Compare previous and current entities for changes
                Diff diff = javers.compare(previousEntity, currentEntity);
                List<Change> changes = diff.getChanges();
                processFieldChanges(entityId, idFieldName, changes, timestamp, revisionTypeDisplayText, changedBy, applicationName, changeLogFieldsMapping, parentTable, changelog);
            }

            previousEntity = currentEntity;
        }

        // Reverse changelog to show latest changes first
        Collections.reverse(changelog);
        return changelog;
    }

    // Determines if the revision is an ADD or DEL operation.
    private boolean isAddOrDel(String revisionTypeDisplayText) {
        return AuditAction.ADD.getDisplayName().equals(revisionTypeDisplayText) || AuditAction.DEL.getDisplayName().equals(revisionTypeDisplayText);
    }

    // Creates an audit log entry for ADD or DEL operations.
    private Map<String, Object> createAuditLogEntryForAddDel(Integer entityId, String idFieldName, Instant timestamp, String revisionTypeDisplayText, Integer changedBy, String applicationName) {
        return AuditLogUtils.createAuditLogEntry(
                entityId, idFieldName, null, null, null, null, timestamp, revisionTypeDisplayText,
                changedBy != null ? String.valueOf(changedBy) : null, applicationName);
    }

    // Processes field changes and adds them to the changelog.
    private void processFieldChanges(Integer entityId, String idFieldName, List<Change> changes, Instant timestamp, String revisionTypeDisplayText, Integer changedBy, String applicationName, Map<String, String> changeLogFieldsMapping, ParentTable parentTable, List<Map<String, Object>> changelog) {
        for (Change change : changes) {
            if (change instanceof ValueChange) {
                ValueChange valueChange = (ValueChange) change;
                String fieldName = valueChange.getPropertyName();
                Object oldValue = valueChange.getLeft();
                Object newValue = valueChange.getRight();
                String changeLogFieldName = changeLogFieldsMapping.getOrDefault(fieldName, fieldName);

                // Retrieve details for the changed field
                ChangeLogFields fieldDetails = this.getChangeLogFieldDetailsByName(changeLogFieldName, true, parentTable);

                if (fieldDetails != null) {
                    Integer dataTypeId = fieldDetails.getDataTypeId();
                    String fieldDisplayName = fieldDetails.getDisplayText();

                    // Handle data types like SIZE and LENGTH (future implementation can be added here)
                    handleFieldDataType(entityId, fieldDetails, dataTypeId);

                    changelog.add(AuditLogUtils.createAuditLogEntry(
                            entityId, idFieldName, fieldDisplayName, changeLogFieldName, oldValue, newValue, timestamp,
                            revisionTypeDisplayText, changedBy != null ? String.valueOf(changedBy) : null, applicationName));
                }
            }
        }
    }

    // Handles specific data types (e.g., SIZE and LENGTH) for field changes.
    private void handleFieldDataType(Integer entityId, ChangeLogFields fieldDetails, Integer dataTypeId) {
        if (dataTypeId != null) {
            if (dataTypeId.equals(FieldDataType.SIZE.getId())) {
                // Handle size field logic here
            } else if (dataTypeId.equals(FieldDataType.LENGTH.getId())) {
                // Handle length field logic here
            }
        }
    }

    // Retrieves application name by ID.
    private String getApplicationName(Integer applicationId) {
        if (applicationId != null) {
            Application application = getApplicationDetailsById(applicationId);
            if (application != null) {
                return application.getApplicationDescription();
            }
        }
        return null;
    }
}
