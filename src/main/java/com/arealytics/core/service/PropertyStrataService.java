package com.arealytics.core.service;

import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.core.domain.empiricalProd.PropertyStrataRelationship;
import com.arealytics.core.repository.empiricalProd.PropertyStrataRelationshipRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class PropertyStrataService {

  private final PropertyStrataRelationshipRepository propertyStrataRelationshipRepository;

  @Transactional
  public void saveStrataMasterProperty(
      Integer masterPropertyId,
      Integer propertyId,
      Boolean isActive,
      Integer loggedInEntityId) {

    // Validate inputs
    if (isActive && masterPropertyId == null) {
      throw new IllegalArgumentException(
          "Master Property ID must not be null when isActive is true");
    }

    // Step 1: Deactivate existing relationship with a different master property
    if (masterPropertyId != null
        && propertyStrataRelationshipRepository
            .existsByStrataProperty_PropertyIDAndIsActiveAndMasterProperty_PropertyIDNot(
                propertyId, true, masterPropertyId)) {
      Optional<PropertyStrataRelationship> existingRelationship = propertyStrataRelationshipRepository
          .findByStrataProperty_PropertyIDAndIsActive(
              propertyId, true);
      if (existingRelationship.isPresent()) {
        PropertyStrataRelationship relationship = existingRelationship.get();
        relationship.setIsActive(false);
        propertyStrataRelationshipRepository.save(relationship);
      }
    }

    // Step 2: Update existing relationship with the same master property
    Optional<PropertyStrataRelationship> existingRelationship = propertyStrataRelationshipRepository
        .findByStrataProperty_PropertyIDAndIsActive(
            propertyId, true);
    if (existingRelationship.isPresent()
        && masterPropertyId != null
        && existingRelationship.get().getMasterProperty().getPropertyID().equals(masterPropertyId)) {

      PropertyStrataRelationship relationship = existingRelationship.get();
      relationship.setIsActive(isActive);
      propertyStrataRelationshipRepository.save(relationship);
    }

    // Step 3: Create new relationship if none exists and isActive is true
    if (!propertyStrataRelationshipRepository.existsByStrataProperty_PropertyIDAndIsActive(
        propertyId, true)
        && isActive
        && masterPropertyId != null) {
      PropertyStrataRelationship newRelationship = new PropertyStrataRelationship();
      newRelationship.getMasterProperty().setPropertyID(masterPropertyId);
      newRelationship.getStrataProperty().setPropertyID(propertyId);
      newRelationship.setIsActive(true);
      propertyStrataRelationshipRepository.save(newRelationship);
    }
  }
}
