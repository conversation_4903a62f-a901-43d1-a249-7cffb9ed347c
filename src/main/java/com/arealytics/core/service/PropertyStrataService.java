package com.arealytics.core.service;

import java.lang.reflect.Array;

import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.domain.empiricalProd.PropertyStrataRelationship;
import com.arealytics.core.dto.request.PropertyStrataRelationshipRequestDTO;
import com.arealytics.core.dto.response.PropertyStrataDetailsDTO;
import com.arealytics.core.dto.response.PropertyStrataRelationshipResponseDTO;
import com.arealytics.core.enumeration.CondoType;
import com.arealytics.core.exception.PropertyStrataException;
import com.arealytics.core.mapper.PropertyStrataMapper;
import com.arealytics.core.repository.empiricalProd.PropertyRepository;
import com.arealytics.core.repository.empiricalProd.PropertyStrataRelationshipRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import com.arealytics.core.dto.querydsl.MasterPropertiesDTO;
import com.arealytics.core.dto.request.PropertyDTO;
import com.arealytics.core.repository.empiricalProd.custom.Impl.PropertyRepositoryImpl;
import com.arealytics.core.utils.UnitConversionUtil;

import jakarta.validation.constraints.Null;

/**
 * Service class for managing Property Strata relationships.
 * Handles the linking of child properties to master properties and maintains 
 * building size relationships between them.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PropertyStrataService {

  private final PropertyStrataRelationshipRepository propertyStrataRelationshipRepository;
  private final PropertyRepository propertyRepository;
  private final PropertyRepositoryImpl propertyRepoImpl;
  private final PropertyStrataMapper mapper;
  private final Validator validator; // JSR-380 Bean Validation

  /**
   * Links multiple child properties to a master property.
   * Uses bean validation to validate input data.
   * 
   * @param relation DTO containing master property ID and comma-separated child property IDs
   * @return List of active property-strata relationships for the master property
   * @throws PropertyStrataException if validation fails
   */
  @Transactional("empiricalProdTransactionManager")
  public List<PropertyStrataRelationshipResponseDTO> linkChildToMaster(PropertyStrataRelationshipRequestDTO relation) {
    
    // Validate using Bean Validation
    validatePropertyStrataRelationshipRequest(relation);
    
    // Parse child property IDs
    List<Integer> childPids;
    try {
      childPids = Arrays.stream(relation.getChildPropertyIds().split(","))
              .map(String::trim)
              .filter(StringUtils::hasText)
              .map(Integer::parseInt)
              .collect(Collectors.toList());
      
      // Check if we have any valid IDs after parsing
      if (childPids.isEmpty()) {
        throw new PropertyStrataException("No valid child property IDs found");
      }
    } catch (NumberFormatException e) {
      throw new PropertyStrataException("Invalid child property ID format - must be comma-separated integers", e);
    }

    // Create relationships for each child property
    for (Integer childPid : childPids) {
      handleRelationship(childPid, relation.getMasterPropertyId(), true, null);
    }

    // Return all active relationships for the master property
    return propertyStrataRelationshipRepository
            .findByMasterProperty_PropertyIDAndIsActiveTrue(relation.getMasterPropertyId())
            .stream()
            .map(this::mapToResponseDTO)
            .collect(Collectors.toList());
  }

  /**
   * Saves a strata master property relationship.
   * Validates inputs before processing.
   * 
   * @param masterPropertyId The ID of the master property
   * @param propertyId The ID of the child property
   * @param isActive Whether the relationship should be active
   * @param loggedInEntityId The ID of the entity making the change
   * @throws PropertyStrataException if validation fails
   */
  @Transactional("empiricalProdTransactionManager")
  public void saveStrataMasterProperty(Integer masterPropertyId, Integer propertyId, Boolean isActive, Integer loggedInEntityId) {
    log.debug("Saving strata master property relationship: master={}, child={}, active={}", masterPropertyId, propertyId, isActive);
    
    handleRelationship(propertyId, masterPropertyId, isActive, loggedInEntityId);
  }

  @Transactional("empiricalProdTransactionManager")
  public List<PropertyStrataDetailsDTO> getLinkedPropertyDetails(Integer propertyId) {
    // Fetch property and validate it
    Property property = propertyRepository.findBypropertyID(propertyId)
        .orElseThrow(() -> new IllegalArgumentException("Property not found"));

    CondoType condoType = property.getPropertyDetails().getCondoTypeID();

    // Throw if CondoType is null or NOT_STRATA
    if (condoType == null || condoType.equals(CondoType.NOT_STRATA)) {
      throw new IllegalArgumentException("CondoTypeID is '%s' for property ID: %d"
          .formatted(condoType, propertyId));
    }

    // Determine if this property is a master
    boolean isMaster = EnumSet.of(CondoType.MASTER_STRATA_RECORD, CondoType.MASTER_FREEHOLD).contains(condoType);

    // If not master, fetch the master property ID via relationship
    Integer masterPropertyId = propertyId;
    if (!isMaster) {
      PropertyStrataRelationship relation = propertyStrataRelationshipRepository
          .findByStrataProperty_PropertyIDAndIsActiveTrue(propertyId)
          .orElseThrow(
              () -> new IllegalArgumentException("No active relationship found for property ID: " + propertyId));
      masterPropertyId = relation.getMasterProperty().getPropertyID();
    }

    // Fetch all active relationships for the master property
    List<PropertyStrataRelationship> relationships = propertyStrataRelationshipRepository
        .findByMasterProperty_PropertyIDAndIsActiveTrue(
            masterPropertyId);

    // Build response list starting with master property details
    List<PropertyStrataDetailsDTO> details = new ArrayList<>();
    if (isMaster) {
      PropertyStrataDetailsDTO masterDto = getStrataDetailsDTO(property);
      details.add(masterDto);
    } else if (relationships != null && !relationships.isEmpty()) {
      PropertyStrataDetailsDTO masterDto = getStrataDetailsDTO(relationships.get(0).getMasterProperty());
      details.add(masterDto);
    }

    // Sort relationships by condo unit number
    relationships.sort(
        Comparator
            // First compare by extracting numeric part from CondoUnit and converting to int
            .comparingInt((PropertyStrataRelationship r) -> {
              // Get the CondoUnit string or default to empty if null
              String condoUnit = Optional.ofNullable(r.getStrataProperty())
                  .map(p -> p.getPropertyDetails().getCondoUnit())
                  .orElse("");
              // Extract numeric part from the CondoUnit string (remove letters, special
              // chars)
              String numericPart = condoUnit.replaceAll("[^0-9]", "");
              // Parse to integer if present, else use 0 for comparison
              return numericPart.isEmpty() ? 0 : Integer.parseInt(numericPart);
            })
            // If numeric parts are equal, use full CondoUnit string as a secondary
            // comparator
            .thenComparing(r -> Optional.ofNullable(r.getStrataProperty())
                .map(p -> p.getPropertyDetails().getCondoUnit())
                .orElse("")));

    // Add each related strata property's details
    relationships.forEach(rel -> details.add(getStrataDetailsDTO(rel.getStrataProperty())));

    return details;
  }

  @Transactional
  public List<MasterPropertiesDTO> getMasterProperties(String searchText, CondoType strataType) {
    if (Arrays.asList(CondoType.NOT_STRATA, CondoType.MASTER_FREEHOLD, CondoType.MASTER_STRATA_RECORD).contains(strataType)) {
      throw new IllegalArgumentException("Cannot find master properties for the given condo type.");
    }
    if (searchText == null || searchText.isEmpty()) {
      return new ArrayList<>();
    }
    final String[] searchTerms = searchText.split("/");
    final String unit = searchTerms.length > 1 ? searchTerms[1] : null;
      return propertyRepoImpl.findMasterPropertiesBySearch(searchText, unit, strataType);
  }

  private PropertyStrataDetailsDTO getStrataDetailsDTO(Property property) {
    PropertyStrataDetailsDTO dto = mapper.toStrataDetailsDto(property);

    // Convert sqft to sqm with null checks
    dto.setBuildingSizeSM(Optional.ofNullable(dto.getBuildingSF()).map(UnitConversionUtil::sqftToSqm).orElse(null));
    dto.setLotSizeSM(Optional.ofNullable(dto.getLotSizeSF()).map(UnitConversionUtil::sqftToSqm).orElse(null));

    return dto;
  }

  /**
   * Validates a PropertyStrataRelationshipRequestDTO using JSR-380 bean validation.
   * 
   * @param request The request DTO to validate
   * @throws PropertyStrataException if validation fails
   */
  private void validatePropertyStrataRelationshipRequest(PropertyStrataRelationshipRequestDTO request) {
    Set<ConstraintViolation<PropertyStrataRelationshipRequestDTO>> violations = validator.validate(request);
    if (!violations.isEmpty()) {
      // Collect all validation errors
      List<String> validationErrors = violations.stream()
          .map(violation -> violation.getPropertyPath() + ": " + violation.getMessage())
          .collect(Collectors.toList());
      
      throw new PropertyStrataException("Validation failed: " + String.join(", ", validationErrors));
    }
  }

  /**
   * Core method for handling property strata relationships.
   * Creates or updates relationships between master and child properties.
   * 
   * @param childPid Child property ID
   * @param masterPid Master property ID
   * @param isActive Whether the relationship should be active
   * @param modifiedById ID of the user making the change
   */
  public void handleRelationship(Integer childPid, Integer masterPid, Boolean isActive, Integer modifiedById) {
    // Additional validation handled through the property validator
    // We're assuming the main validation was done in the public methods
    // This is just a safety check for internal calls
    if (childPid == null) {
      throw new PropertyStrataException("Child Property ID cannot be null");
    }

    // Fetch child property
    Property child = fetchProperty(childPid, "child");
    CondoType childCondo = child.getPropertyDetails().getCondoTypeID();

  Optional<PropertyStrataRelationship> existingRelation =
            propertyStrataRelationshipRepository.findByStrataProperty_PropertyIDAndIsActive(childPid, true);

        // Allow masterPid to be null for specific condo types and deactivate existing relationships
        if (isActive && masterPid == null) {
            if (childCondo != CondoType.MASTER_FREEHOLD && 
                childCondo != CondoType.MASTER_STRATA_RECORD && 
                childCondo != CondoType.NOT_STRATA) {
                throw new PropertyStrataException("Master Property ID must not be null when isActive is true");
            }
            // Deactivate existing relationship if it exists
            if (existingRelation.isPresent()) {
                existingRelation.get().setIsActive(false);
                Integer prevMasterPropertyId = existingRelation.get().getMasterPropertyId();
                adjustPreviousMasterSize(prevMasterPropertyId, child);
                propertyStrataRelationshipRepository.save(existingRelation.get());
            }
            // Exit early since no new relationship needs to be created
            return;
        }

    // Break existing strata relationship when no masterPid is provided and isActive is false
    if (masterPid == null && existingRelation.isPresent()) {
      existingRelation.get().setIsActive(false);
    Integer prevMasterPropertyId = existingRelation.get().getMasterPropertyId();

      adjustPreviousMasterSize(prevMasterPropertyId, child);
      propertyStrataRelationshipRepository.save(existingRelation.get());
      return;
    }
    // Fetch master property
    Property master = fetchProperty(masterPid, "master");

    // Prevent linking a property to itself
    if (masterPid.equals(childPid)) {
      throw new PropertyStrataException("A property cannot be linked to itself");
    }

    // Validate condo types
    CondoType masterCondo = master.getPropertyDetails().getCondoTypeID();
    validateCondoTypes(masterCondo, childCondo);

    // Get building sizes
    BigDecimal masterSize = Optional.ofNullable(master.getPropertySize().getBuildingSizeSF()).orElse(BigDecimal.ZERO);
    BigDecimal childSize = Optional.ofNullable(child.getPropertySize().getBuildingSizeSF()).orElse(BigDecimal.ZERO);

    // Check if there's already an active relationship between these properties
    PropertyStrataRelationship activeRelation =
            propertyStrataRelationshipRepository
                    .findByMasterProperty_PropertyIDAndStrataProperty_PropertyIDAndIsActiveTrue(masterPid, childPid);

    // Find all existing relationships for the child property
    List<PropertyStrataRelationship> existingRelations =
            propertyStrataRelationshipRepository.findByStrataProperty_PropertyID(childPid);

    boolean isNewRelation = existingRelations == null || existingRelations.isEmpty();
    
    if (isNewRelation) {
      // Create a new relationship
      PropertyStrataRelationship newRelation = createRelationship(masterPid, childPid, true, modifiedById);
      updateChildCondoType(masterCondo, childCondo, child);
      propertyStrataRelationshipRepository.save(newRelation);
      
      // Update master property size
      updateMasterPropertySize(master, masterSize, childSize, true);
    } else {
      if (activeRelation == null) {
        // No active relationship exists, handle existing inactive ones
        boolean masterExists = false;

        for (PropertyStrataRelationship relation : existingRelations) {
          if (relation.getMasterProperty().getPropertyID().equals(masterPid)) {
            // Reactivate existing relation with this master
            relation.setIsActive(true);
            updateChildCondoType(masterCondo, childCondo, child);
            if (modifiedById != null) {
              // Set modifiedBy if available
              // relation.setModifiedBy(modifiedById);
            }
            masterExists = true;
            
            // Update master property size
            updateMasterPropertySize(master, masterSize, childSize, true);
          } else if (relation.getIsActive()) {
            // Deactivate other active relations
            relation.setIsActive(false);
            if (modifiedById != null) {
              // Set modifiedBy if available
              // relation.setModifiedBy(modifiedById);
            }
            // Adjust the previous master's size
            adjustPreviousMasterSize(relation.getMasterProperty().getPropertyID(), child);
          }
        }
        // Save all existing relations after modification
        propertyStrataRelationshipRepository.saveAll(existingRelations);
        // Create new relation if this master doesn't exist in previous relations
        if (!masterExists) {
          PropertyStrataRelationship newRelation = createRelationship(masterPid, childPid, true, modifiedById);
          propertyStrataRelationshipRepository.save(newRelation);
          // Update master property size
          updateMasterPropertySize(master, masterSize, childSize, true);
        }
      } else {
        // Already active, update master size
        log.debug("Relationship already active between master={} and child={}", masterPid, childPid);
        updateMasterPropertySize(master, masterSize, childSize, true);
      }
    }
  }

  /**
   * Creates a new PropertyStrataRelationship entity.
   * 
   * @param masterPid Master property ID
   * @param childPid Child property ID
   * @param isActive Whether the relationship is active
   * @param modifiedById ID of the user creating the relationship
   * @return New PropertyStrataRelationship entity
   */
  private PropertyStrataRelationship createRelationship(Integer masterPid, Integer childPid, boolean isActive, Integer modifiedById) {
    PropertyStrataRelationship relation = new PropertyStrataRelationship();
    relation.setMasterProperty(fetchProperty(masterPid, "master"));
    relation.setStrataProperty(fetchProperty(childPid, "child"));
    relation.setIsActive(isActive);
    if (modifiedById != null) {
      // Set createdBy if available
      // relation.setCreatedBy(modifiedById);
    }
    return relation;
  }

  /**
   * Updates the size of a master property based on child property addition or removal.
   * 
   * @param master The master property
   * @param currentSize Current size of the master property
  * @param childSize Size of the child property
   * @param isAdding Whether adding (true) or removing (false) the child property
   */
  private void updateMasterPropertySize(Property master, BigDecimal currentSize, BigDecimal childSize, boolean isAdding) {
    BigDecimal newSize = isAdding ? 
        currentSize.add(childSize) : 
        currentSize.subtract(childSize);
    
    // Ensure size doesn't go negative
    if (newSize.compareTo(BigDecimal.ZERO) < 0) {
      newSize = BigDecimal.ZERO;
    }
    
    master.getPropertySize().setBuildingSizeSF(newSize);
    propertyRepository.save(master);
  }

  /**
   * Adjusts the size of a previous master property when a child is removed.
   * 
   * @param previousMasterId ID of the previous master property
   * @param child Child property being removed
   */
  private void adjustPreviousMasterSize(Integer previousMasterId, Property child) {
    Property previousMaster = propertyRepository.findBypropertyIDAndIsActiveTrue(previousMasterId);
    if (previousMaster != null) {
      BigDecimal masterSize = Optional.ofNullable(previousMaster.getPropertySize().getBuildingSizeSF()).orElse(BigDecimal.ZERO);
      BigDecimal childSize = Optional.ofNullable(child.getPropertySize().getBuildingSizeSF()).orElse(BigDecimal.ZERO);
      
      updateMasterPropertySize(previousMaster, masterSize, childSize, false);
    }
  }

  /**
   * Validates the compatibility of condo types for a strata relationship.
   * 
   * @param masterCondo Condo type of the master property
   * @param childCondo Condo type of the child property
   * @throws PropertyStrataException if condo types are incompatible
   */
  private void validateCondoTypes(CondoType masterCondo, CondoType childCondo) {
    if (masterCondo == null) {
      throw new PropertyStrataException("Master condo type is null");
    }

    if (!(masterCondo == CondoType.MASTER_STRATA_RECORD || masterCondo == CondoType.MASTER_FREEHOLD)) {
      throw new PropertyStrataException("Cannot link child property: the selected master property is not a Master Strata or MasterFreehold");
    }

    if (childCondo == null) {
      throw new PropertyStrataException("Child condo type is null");
    }

    if (masterCondo == CondoType.MASTER_STRATA_RECORD) {
      if (childCondo != CondoType.STRATA && childCondo != CondoType.NOT_STRATA) {
        throw new PropertyStrataException("Cannot form relation, as child condo type " + childCondo + 
            " is incompatible with master condo type " + masterCondo);
      }
    }

    if (masterCondo == CondoType.MASTER_FREEHOLD) {
      if (childCondo != CondoType.CHILD_FREEHOLD && childCondo != CondoType.NOT_STRATA) {
        throw new PropertyStrataException("Cannot form relation, as child condo type " + childCondo + 
            " is incompatible with master condo type " + masterCondo);
      }
    }
  }

  /**
   * Fetches a property by ID, with descriptive error message if not found.
   * 
   * @param propertyId ID of the property to fetch
   * @param label Label for the property (master/child) to use in error messages
   * @return Property entity
   * @throws PropertyStrataException if property not found
   */
  private Property fetchProperty(Integer propertyId, String label) {
    if (propertyId == null) {
      throw new PropertyStrataException(label + " Property ID cannot be null");
    }
    
    Property property = propertyRepository.findBypropertyIDAndIsActiveTrue(propertyId);
    if (property == null) {
      throw new PropertyStrataException("No property found for the given " + label + " Property ID: " + propertyId);
    }
    return property;
  }

  private void updateChildCondoType(CondoType masterCondo, CondoType childCondo, Property child) {
    if (masterCondo == CondoType.MASTER_FREEHOLD) {
      if (childCondo == CondoType.NOT_STRATA || childCondo == null) {
        child.getPropertyDetails().setCondoTypeID(CondoType.CHILD_FREEHOLD);
        propertyRepository.save(child);
      }
    }
    if (masterCondo == CondoType.MASTER_STRATA_RECORD) {
      if (childCondo == CondoType.NOT_STRATA || childCondo == null) {
        child.getPropertyDetails().setCondoTypeID(CondoType.STRATA);
        propertyRepository.save(child);
      }
    }
  }

  /**
   * Maps a PropertyStrataRelationship entity to its DTO representation.
   * 
   * @param entity PropertyStrataRelationship entity
   * @return PropertyStrataRelationshipResponseDTO
   */
  private PropertyStrataRelationshipResponseDTO mapToResponseDTO(PropertyStrataRelationship entity) {
    return mapper.toDto(entity);
  }
}