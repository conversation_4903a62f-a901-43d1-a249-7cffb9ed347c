package com.arealytics.core.service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.core.domain.empiricalProd.ParcelProperty;
import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.dto.request.ParcelPropertyRequestDTO;
import com.arealytics.core.dto.response.ParcelPropertyDTO;
import com.arealytics.core.mapper.ParcelPropertyMapper;
import com.arealytics.core.repository.empiricalProd.ParcelPropertyRepository;
import com.arealytics.core.repository.empiricalProd.PropertyRepository;
import com.arealytics.core.utils.UnitConversionUtil;

@Service
public class ParcelService {
    private final ParcelPropertyMapper mapper;
    private final ParcelPropertyRepository parcelPropertyRepository;
    private final PropertyRepository propertyRepository;

    @Autowired
    public ParcelService(
            @Lazy ParcelPropertyRepository parcelPropertyRepository,
            @Lazy ParcelPropertyMapper mapper,
            PropertyRepository propertyRepository) {
        this.parcelPropertyRepository = parcelPropertyRepository;
        this.mapper = mapper;
        this.propertyRepository = propertyRepository;
    }

    @Transactional("empiricalProdTransactionManager") // Ensuring EmpiricalProd datasource is used
    public List<ParcelPropertyDTO> getParcelByProperty(Integer propertyId) {
        return parcelPropertyRepository
                .findByPropertyIdAndParcel_IsActive(propertyId, true)
                .stream()
                .map(this::enhanceDTO)
                .toList();
    }

    @Transactional("empiricalProdTransactionManager") // Ensuring EmpiricalProd datasource is used
    public ParcelPropertyDTO createParcel(ParcelPropertyRequestDTO requestDTO, Integer propertyId) {

        return saveParcelProperty(requestDTO, new ParcelProperty(), propertyId, null);
    }

    @Transactional("empiricalProdTransactionManager") // Ensuring EmpiricalProd datasource is used
    public ParcelPropertyDTO updateParcel(
            ParcelPropertyRequestDTO parcelPropertyDTO, Integer parcelId, Integer propertyId) {

        ParcelProperty parcelProperty =
                parcelPropertyRepository
                        .findByParcel_ParcelIdAndParcel_IsActive(parcelId, true)
                        .orElseThrow(() -> new IllegalArgumentException("Parcel not found"));

        if (!Objects.equals(parcelProperty.getPropertyId(), propertyId)) {
            throw new IllegalArgumentException("Parcel does not belong to the given property");
        }

        return saveParcelProperty(parcelPropertyDTO, parcelProperty, propertyId, parcelId);
    }

    @Transactional("empiricalProdTransactionManager") // Ensuring EmpiricalProd datasource is used
    public void deleteParcel(Integer parcelId, Integer propertyId) {
        ParcelProperty parcelProperty =
                parcelPropertyRepository
                        .findByParcel_ParcelIdAndParcel_IsActive(parcelId, true)
                        .orElseThrow(() -> new IllegalArgumentException("Parcel not found"));

        if (!Objects.equals(parcelProperty.getPropertyId(), propertyId)) {
            throw new IllegalArgumentException("Parcel does not belong to the given property");
        }

        parcelProperty.getParcel().setIsActive(false);
        parcelPropertyRepository.save(parcelProperty);
        this.saveParcelInfo(propertyId);
    }

    private void saveParcelInfo(Integer propertyId) {
        Property property =
                propertyRepository
                        .findBypropertyID(propertyId)
                        .orElseThrow(() -> new IllegalArgumentException("Property not found"));
        // Set comma-separated parcel info
        String parcelInfo =
                getParcelByProperty(propertyId).stream()
                        .map(ParcelPropertyDTO::getParcelNo)
                        .filter(Objects::nonNull)
                        .filter(no -> !no.isEmpty())
                        .collect(Collectors.joining(","));

        property.getPropertyDetails().setParcelInfo(parcelInfo);
        propertyRepository.save(property);
    }

    private ParcelPropertyDTO saveParcelProperty(
            ParcelPropertyRequestDTO requestDTO,
            ParcelProperty currentParcelProperty,
            Integer propertyId,
            Integer parcelId) {

        ParcelProperty parcelProperty = mapper.toEntity(requestDTO, currentParcelProperty);
        parcelProperty.setPropertyId(propertyId);
        parcelProperty.getParcel().setParcelId(parcelId);
        parcelProperty.getParcel().setIsActive(true);

        ParcelProperty savedParcelProperty = parcelPropertyRepository.save(parcelProperty);

        this.saveParcelInfo(propertyId);
        return enhanceDTO(savedParcelProperty);
    }

    private ParcelPropertyDTO enhanceDTO(ParcelProperty parcelProperty) {
        ParcelPropertyDTO dto = mapper.toDTO(parcelProperty);
        if (parcelProperty.getParcel().getParcelSize() != null) {
            dto.setParcelSizeSM(
                    UnitConversionUtil.sqftToSqm(
                            parcelProperty.getParcel().getParcelSize().doubleValue()));
        }
        return dto;
    }
}
