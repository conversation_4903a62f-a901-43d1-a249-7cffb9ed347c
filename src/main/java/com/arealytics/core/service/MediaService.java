package com.arealytics.core.service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import org.springframework.stereotype.Service;

import com.arealytics.core.domain.empiricalProd.CompanyRelationship;
import com.arealytics.core.domain.empiricalProd.Media;
import com.arealytics.core.domain.empiricalProd.MediaRelationship;
import com.arealytics.core.dto.response.MediaDTO;
import com.arealytics.core.enumeration.CondoType;
import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.enumeration.Role;
import com.arealytics.core.mapper.MediaMapper;
import com.arealytics.core.repository.empiricalProd.CompanyRelationshipRepository;
import com.arealytics.core.repository.empiricalProd.MediaRelationshipRepository;
import com.arealytics.core.repository.empiricalProd.PropertyRepository;
import com.arealytics.core.repository.empiricalProd.PropertyStrataRelationshipRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class MediaService {

  // Repositories and mapper for dependency injection
  private final MediaRelationshipRepository mediaRelationshipRepository;
  private final CompanyRelationshipRepository companyRelationshipRepository;
  private final PropertyRepository propertyRepository;
  private final PropertyStrataRelationshipRepository propertyStrataRelationshipRepository;
  private final MediaMapper mapper;

  // TODO: Replace with user session or security context
  private static final Integer LOGIN_ENTITY_ID = 37754;
  private static final Integer PERSON_ID = 35905;
  private static final Integer BRANCH_ID = 2;
  private static final Integer PARENT_COMPANY_ID = 1;
  private static final Role CURRENT_ROLE = Role.INTERNAL;

  /**
   * Returns all MediaDTOs for the given mediaRelationType and relationId.
   *
   * @param mediaRelationType the type of relation (e.g., PROPERTY, COMPANY, etc.)
   * @param relationId        the ID of the related entity
   * @return list of media DTOs with edit permissions and building size info if
   *         applicable
   */
  public List<MediaDTO> getMediaByRelationId(
      MediaRelationType mediaRelationType, Integer relationId) {
    List<Integer> relationIds = new ArrayList<>();
    AtomicReference<CondoType> condoTypeRef = new AtomicReference<>();

    // Adjust relationType if ALL_MEDIA is requested — fallback to ENTITY
    MediaRelationType effectiveType = mediaRelationType;
    Integer effectiveRelationId;

    if (mediaRelationType == MediaRelationType.ALL_MEDIA) {
      effectiveType = MediaRelationType.ENTITY;
      effectiveRelationId = PERSON_ID;
    } else {
      effectiveRelationId = relationId;
    }

    // Add base relationId
    relationIds.add(effectiveRelationId);

    // Special case for MASTER_FREEHOLD: add related child strata property IDs
    if (effectiveType == MediaRelationType.PROPERTY) {
      propertyRepository
          .findBypropertyID(effectiveRelationId)
          .ifPresent(
              property -> {
                CondoType condoType = property.getPropertyDetails().getCondoTypeID();
                if (condoType == CondoType.MASTER_FREEHOLD) {
                  condoTypeRef.set(condoType);
                  propertyStrataRelationshipRepository
                      .findByMasterProperty_PropertyIDAndIsActive(
                          effectiveRelationId, true)
                      .forEach(
                          strata -> relationIds.add(
                              strata.getStrataProperty()
                                  .getPropertyID()));
                }
              });
    }

    // Fetch all media relationships for the provided media relation type and relation IDs
    List<MediaRelationship> mediaRelationships = mediaRelationshipRepository.findByMediaRelationTypeIdAndRelationIdIn(
        effectiveType, relationIds);

    // Extract unique company IDs from media relationships - To check edit access
    Set<Integer> companyIds = new HashSet<>();
    mediaRelationships.forEach(
        relationship -> companyIds.add(
            relationship
                .getMedia()
                .getCreatedBy()
                .getCompany()
                .getCompanyId()));

    // If a role is COMPANY_SUPER_USER, fetch company relationship hierarchy
    List<CompanyRelationship> companyRelationships = new ArrayList<>();
    if (CURRENT_ROLE == Role.COMPANY_SUPER_USER) {
      companyRelationships = companyRelationshipRepository.findByChildCompany_CompanyIdInAndIsActive(
          new ArrayList<>(companyIds), true);
    }

    // Prepare final list of media DTOs
    List<MediaDTO> mediaDTOs = new ArrayList<>();
    for (MediaRelationship relationship : mediaRelationships) {
      Media media = relationship.getMedia();

      // Convert to DTO using mapper
      MediaDTO dto = mapper.toDTO(relationship);

      // Determine user edit permission based on role and ownership logic
      boolean hasEdit = (effectiveType == MediaRelationType.PROPERTY && CURRENT_ROLE == Role.INTERNAL)
          || (CURRENT_ROLE == Role.LISTING_AGENT
              && Objects.equals(
                  media.getCreatedBy().getEntityId(), LOGIN_ENTITY_ID))
          || (CURRENT_ROLE == Role.BRANCH_SUPER_USER
              && Objects.equals(
                  media.getCreatedBy().getCompany().getCompanyId(),
                  BRANCH_ID))
          || (CURRENT_ROLE == Role.COMPANY_SUPER_USER
              && companyRelationships.stream()
                  .anyMatch(
                      rel -> Objects.equals(
                          rel.getParentCompany()
                              .getCompanyId(),
                          PARENT_COMPANY_ID)));

      dto.setHasEdit(hasEdit); // Set permission flag

      // If the original property was a MASTER_FREEHOLD, set building SF info
      if (condoTypeRef.get() == CondoType.MASTER_FREEHOLD
          && effectiveType == MediaRelationType.PROPERTY) {
        BigDecimal buildingSF = Optional.ofNullable(relationship.getProperty())
            .map(p -> p.getPropertySize().getBuildingSizeSF())
            .orElse(null);
        dto.setBuildingSizeSF(buildingSF);
      }

      mediaDTOs.add(dto); // Add to a final list
    }

    return mediaDTOs;
  }
}
