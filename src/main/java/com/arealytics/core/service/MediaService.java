package com.arealytics.core.service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.core.constants.CacheNames;
import com.arealytics.core.domain.empiricalProd.CompanyRelationship;
import com.arealytics.core.domain.empiricalProd.EntityModel;
import com.arealytics.core.domain.empiricalProd.Media;
import com.arealytics.core.domain.empiricalProd.MediaRelationship;
import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.dto.request.MediaDeleteRequestDTO;
import com.arealytics.core.dto.request.MediaRequestDTO;
import com.arealytics.core.dto.response.MediaDTO;
import com.arealytics.core.enumeration.CondoType;
import com.arealytics.core.enumeration.MediaRelationType;
import com.arealytics.core.enumeration.Role;
import com.arealytics.core.mapper.MediaMapper;
import com.arealytics.core.repository.empiricalProd.CompanyRelationshipRepository;
import com.arealytics.core.repository.empiricalProd.MediaRelationshipRepository;
import com.arealytics.core.repository.empiricalProd.PropertyRepository;
import com.arealytics.core.repository.empiricalProd.PropertyStrataRelationshipRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class MediaService {

  // Repositories and mapper for dependency injection
  private final MediaRelationshipRepository mediaRelationshipRepository;
  private final CompanyRelationshipRepository companyRelationshipRepository;
  private final PropertyRepository propertyRepository;
  private final PropertyStrataRelationshipRepository propertyStrataRelationshipRepository;
  private final MediaMapper mapper;
  private final PropertyService propertyService;

  // TODO: Replace with user session or security context
  private static final Integer LOGIN_ENTITY_ID = 37754;
  private static final Integer PERSON_ID = 35905;
  private static final Integer BRANCH_ID = 2;
  private static final Integer PARENT_COMPANY_ID = 1;
  private static final Role CURRENT_ROLE = Role.INTERNAL;

  /**
   * Returns all MediaDTOs for the given mediaRelationType and relationId.
   *
   * @param mediaRelationType the type of relation (e.g., PROPERTY, COMPANY, etc.)
   * @param relationId        the ID of the related entity
   * @return list of media DTOs with edit permissions and building size info if
   *         applicable
   */
  @Transactional("empiricalProdTransactionManager") // Ensuring EmpiricalProd datasource is used
  @Cacheable(value = CacheNames.MEDIA, key = "#mediaRelationType + ':' + #relationId")
  public List<MediaDTO> getMediaByRelationId(
      MediaRelationType mediaRelationType, Integer relationId) {
    List<Integer> relationIds = new ArrayList<>();
    AtomicReference<CondoType> condoTypeRef = new AtomicReference<>();

    // Adjust relationType if ALL_MEDIA is requested — fallback to ENTITY
    MediaRelationType effectiveType = mediaRelationType;
    Integer effectiveRelationId;

    if (mediaRelationType == MediaRelationType.ALL_MEDIA) {
      effectiveType = MediaRelationType.ENTITY;
      effectiveRelationId = PERSON_ID;
    } else {
      effectiveRelationId = relationId;
    }

    // Add base relationId
    relationIds.add(effectiveRelationId);

    // Special case for MASTER_FREEHOLD: add related child strata property IDs
    if (effectiveType == MediaRelationType.PROPERTY) {
      propertyRepository
          .findBypropertyID(effectiveRelationId)
          .ifPresent(
              property -> {
                CondoType condoType = property.getPropertyDetails().getCondoTypeID();
                if (condoType == CondoType.MASTER_FREEHOLD) {
                  condoTypeRef.set(condoType);
                  propertyStrataRelationshipRepository
                      .findByMasterProperty_PropertyIDAndIsActive(
                          effectiveRelationId, true)
                      .forEach(
                          strata -> relationIds.add(
                              strata.getStrataProperty()
                                  .getPropertyID()));
                }
              });
    }

    // Fetch all media relationships for the provided media relation type and
    // relation IDs
    List<MediaRelationship> mediaRelationships = mediaRelationshipRepository.findByMediaRelationTypeIdAndRelationIdInAndIsActiveTrue(
        effectiveType, relationIds);

    // Extract unique company IDs from media relationships - To check edit access
    Set<Integer> companyIds = new HashSet<>();
    mediaRelationships.forEach(
        relationship -> companyIds.add(
            relationship
                .getMedia()
                .getCreatedBy()
                .getCompany()
                .getCompanyId()));

    // If a role is COMPANY_SUPER_USER, fetch company relationship hierarchy
    List<CompanyRelationship> companyRelationships = new ArrayList<>();
    if (CURRENT_ROLE == Role.COMPANY_SUPER_USER) {
      companyRelationships = companyRelationshipRepository.findByChildCompany_CompanyIdInAndIsActive(
          new ArrayList<>(companyIds), true);
    }

    // Prepare final list of media DTOs
    List<MediaDTO> mediaDTOs = new ArrayList<>();
    for (MediaRelationship relationship : mediaRelationships) {
      Media media = relationship.getMedia();

      // Convert to DTO using mapper
      MediaDTO dto = mapper.toDTO(relationship);

      // Determine user edit permission based on role and ownership logic
      boolean hasEdit = (effectiveType == MediaRelationType.PROPERTY && CURRENT_ROLE == Role.INTERNAL)
          || (CURRENT_ROLE == Role.LISTING_AGENT
              && Objects.equals(
                  media.getCreatedBy().getEntityId(), LOGIN_ENTITY_ID))
          || (CURRENT_ROLE == Role.BRANCH_SUPER_USER
              && Objects.equals(
                  media.getCreatedBy().getCompany().getCompanyId(),
                  BRANCH_ID))
          || (CURRENT_ROLE == Role.COMPANY_SUPER_USER
              && companyRelationships.stream()
                  .anyMatch(
                      rel -> Objects.equals(
                          rel.getParentCompany()
                              .getCompanyId(),
                          PARENT_COMPANY_ID)));

      dto.setHasEdit(hasEdit); // Set permission flag

      // If the original property was a MASTER_FREEHOLD, set building SF info
      if (condoTypeRef.get() == CondoType.MASTER_FREEHOLD
          && effectiveType == MediaRelationType.PROPERTY) {
        BigDecimal buildingSF = Optional.ofNullable(relationship.getProperty())
            .map(p -> p.getPropertySize().getBuildingSizeSF())
            .orElse(null);
        dto.setBuildingSizeSF(buildingSF);
      }

      mediaDTOs.add(dto); // Add to a final list
    }

    return mediaDTOs;
  }


  @CacheEvict(value = CacheNames.MEDIA, key = "#mediaRequestDTO.mediaRelationTypeId + ':' + #mediaRequestDTO.relationId")
  @Transactional("empiricalProdTransactionManager") // Ensure EmpiricalProd datasource is used
  public MediaDTO createMedia(MediaRequestDTO mediaRequestDTO) {

    // Map DTO to entity
    final MediaRelationship mediaRelationship = mapper.toEntity(mediaRequestDTO, new MediaRelationship());

    // Fetch property or fail fast
    Property property = propertyRepository.findBypropertyID(mediaRequestDTO.getPropertyId())
        .orElseThrow(() -> new IllegalArgumentException("Property not found"));

    // Handle default media update
    if (mediaRelationship.getIsDefault()) {
      unsetExistingDefaultMedia(mediaRequestDTO, mediaRelationship.getMedia().getMediaId());
      property.getPropertyDetails().setMainPhotoUrl(mediaRequestDTO.getPath());
    }

    // Save media and updated property
      return saveMedia(mediaRelationship, property);
  }

  /**
   * Updates an existing media entity with new values.
   * If it's marked as default, unsets existing defaults and updates a property main
   * photo.
   * If STRATA type, cascades the default photo to master property.
   */
  @Transactional("empiricalProdTransactionManager") // Ensure EmpiricalProd datasource is used

  @CacheEvict(value = CacheNames.MEDIA, key = "#mediaRequestDTO.mediaRelationTypeId + ':' + #mediaRequestDTO.relationId")
  public MediaDTO updateMedia(MediaRequestDTO mediaRequestDTO, Integer mediaId) {

    // Fetch existing MediaRelationship or fail fast
    MediaRelationship existingMediaRelationship = mediaRelationshipRepository
        .findByMedia_MediaIdAndIsActiveTrue(mediaId)
        .orElseThrow(() -> new IllegalArgumentException("Media not found"));

    // Map incoming DTO values into the existing media entity
    MediaRelationship mediaRelationship = mapper.toEntity(mediaRequestDTO, existingMediaRelationship);

    // Load associated property or throw if missing
    Property property = propertyRepository.findBypropertyID(mediaRequestDTO.getPropertyId())
        .orElseThrow(() -> new IllegalArgumentException("Property not found"));

    // Reset media ID if property changes and is a MASTER_FREEHOLD
    if (propertyService.isMasterFreehold(property) && !isSameProperty(property, mediaRelationship) && mediaRelationship.getIsDefault()) {
      mediaRelationship.getMedia().setMediaId(null); // force insert instead of update
    }

    // If new media is default, unset previous defaults and update property's main
    // photo
    if (mediaRelationship.getIsDefault()) {
      unsetExistingDefaultMedia(mediaRequestDTO, mediaRelationship.getMedia().getMediaId()); // updates other entries
      property.getPropertyDetails().setMainPhotoUrl(mediaRequestDTO.getPath());
    }

    // Save updated media and property
      return saveMedia(mediaRelationship, property);
  }

  @Transactional("empiricalProdTransactionManager") // Ensure EmpiricalProd datasource is used
  @CacheEvict(value = CacheNames.MEDIA, key = "#mediaDeleteRequestDTO.mediaRelationTypeId + ':' + #mediaDeleteRequestDTO.relationId")
  public void deleteMedia(MediaDeleteRequestDTO mediaDeleteRequestDTO) {
    // Fetch existing media relationship or fail fast
    MediaRelationship existingMediaRelationship = mediaRelationshipRepository
        .findByMediaRelationshipIdAndIsActiveTrue(mediaDeleteRequestDTO.getMediaRelationshipId())
        .orElseThrow(() -> new IllegalArgumentException("Media not found"));

    // Set ModifiedBy
    EntityModel modifiedBy = new EntityModel();
    modifiedBy.setEntityId(LOGIN_ENTITY_ID);
    existingMediaRelationship.getMedia().setModifiedBy(modifiedBy);

    // Mark as inactive
    existingMediaRelationship.setIsActive(false);
    mediaRelationshipRepository.save(existingMediaRelationship);
  }

  // Utility method to check if property and media refer to the same property ID
  private boolean isSameProperty(Property property, MediaRelationship mediaRelationship) {
    return Objects.equals(property.getPropertyID(), mediaRelationship.getProperty().getPropertyID());
  }

  /**
   * Unsets existing default media for a given relation ID and type.
   */
  private void unsetExistingDefaultMedia(MediaRequestDTO dto, Integer mediaId) {
    List<MediaRelationship> defaultMediaList = mediaRelationshipRepository
        .findByRelationIdAndMediaRelationTypeIdAndIsDefaultTrueAndIsActiveTrue(
            dto.getRelationId(),
            dto.getMediaRelationTypeId());

    defaultMediaList.forEach(existing -> {
      if (!existing.getMediaId().equals(mediaId)) {
        existing.setIsDefault(false);
        mediaRelationshipRepository.save(existing);
      }
    });
  }

  /**
   *  Todo: find out the case and reuse or remove this
   * Creates a master-level default media if the current property is STRATA and no
   * default exists for master.
   */
  private void createMasterDefaultMedia(MediaRequestDTO dto, Property property) {

    if (!Objects.equals(property.getPropertyDetails().getCondoTypeID(), CondoType.STRATA))
      return;
    if (!Objects.equals(dto.getMediaRelationTypeId(), MediaRelationType.PROPERTY))
      return;

    propertyStrataRelationshipRepository
        .findByStrataProperty_PropertyIDAndIsActive(property.getPropertyID(), true)
        .ifPresent(strataRel -> {

          // Check if there's already a default media for the master
          List<MediaRelationship> masterMediaList = mediaRelationshipRepository
              .findByRelationIdAndMediaRelationTypeIdAndIsDefaultTrueAndIsActiveTrue(
                  strataRel.getMasterProperty().getPropertyID(),
                  MediaRelationType.PROPERTY);

          if (masterMediaList.isEmpty()) {
            // Create new media for master property
            Integer masterId = strataRel.getMasterProperty().getPropertyID();

            // Clone DTO with master IDs
            MediaRequestDTO masterDTO = dto.toBuilder().propertyId(masterId).relationId(masterId).build();

            strataRel.getMasterProperty().getPropertyDetails().setMainPhotoUrl(dto.getPath());
            MediaRelationship masterMedia = mapper.toEntity(masterDTO, new MediaRelationship());
            saveMedia(masterMedia, strataRel.getMasterProperty());
          }
        });
  }

  // Saves media and updates the property with the modifiedBy entity
  private MediaDTO saveMedia(MediaRelationship mediaRelationship, Property property) {
    mediaRelationship.setIsActive(true);

    EntityModel modifiedBy = new EntityModel();
    modifiedBy.setEntityId(LOGIN_ENTITY_ID);

    // Set auditing information
    // Todo : this should automatically be set global level
    if (mediaRelationship.getMedia().getMediaId() == null) {
      mediaRelationship.getMedia().setCreatedBy(modifiedBy);
    } else {
      mediaRelationship.getMedia().setModifiedBy(modifiedBy);
    }
    property.setModifiedBy(modifiedBy);

    // Persist entities
    MediaRelationship saved = mediaRelationshipRepository.save(mediaRelationship);
    propertyRepository.save(property);

    return mapper.toDTO(saved);
  }

}
