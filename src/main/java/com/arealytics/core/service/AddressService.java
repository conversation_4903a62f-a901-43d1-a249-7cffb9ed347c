package com.arealytics.core.service;

import java.util.Optional;

import org.springframework.stereotype.Service;

import com.arealytics.core.domain.empiricalProd.Address;
import com.arealytics.core.dto.request.PropertyDTO;
import com.arealytics.core.mapper.AddressMapper;
import com.arealytics.core.repository.empiricalProd.AddressRepository;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class AddressService {

    private final AddressRepository addressRepository;
    private final AddressMapper addressMapper;

    @Transactional
    public Address createAddress(PropertyDTO propertyDTO, Integer locationId, Integer propertyId) {
        Address address = addressMapper.toAddress(propertyDTO, locationId, propertyId);
        return addressRepository.save(address);
    }

    @Transactional
    public Address updateAddress(PropertyDTO propertyDTO, Integer locationId, Integer propertyId) {

        Address address =
                addressRepository
                        .findFirstByParentIdAndIsActiveTrueOrderByAddressIdDesc(propertyId)
                        .stream()
                        .findFirst()
                        .orElseThrow(
                                () ->
                                        new RuntimeException(
                                                "No active address found for property: "
                                                        + propertyId));

        address = addressMapper.updateAddressFromDto(propertyDTO, address);
        return addressRepository.save(address);
    }

    /**
     * Finds an active address by parent ID.
     *
     * @param propertyId The ID of the property
     * @return An Optional containing the active address, if found
     */
    public Optional<Address> findByParentIdAndIsActiveTrue(Integer propertyId) {
        return addressRepository.findFirstByParentIdAndIsActiveTrueOrderByAddressIdDesc(propertyId);
    }
}
