package com.arealytics.core.service;

import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.core.domain.empiricalProd.Country;
import com.arealytics.core.dto.response.CountryDTO;
import com.arealytics.core.enumeration.*;
import com.arealytics.core.mapper.CountryMapper;
import com.arealytics.core.repository.empiricalProd.*;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
@Cacheable("lookupCache")
public class LookupService {
    private final CityRepository cityRepository;
    private final CountyRepository countyRepository;
    private final SuffixRepository suffixRepository;
    private final StateRepository stateRepository;
    private final SubMarketRepository subMarketRepository;
    private final ZipCodeRepository zipCodeRepository;
    private final SpecificUsesRepository specificUsesRepository;
    private final MarketRepository marketRepository;
    private final CountryRepository countryRepository;
    private final CountryMapper countryMapper;

    private Integer countryId = 14;

    /**
     * Retrieves all lookup data for the application
     *
     * @return Map containing all lookup values organized by key
     */
    @Transactional("empiricalProdTransactionManager")
    public Map<String, Object> getAllLookups() {
        Map<String, Object> lookupData = new HashMap<>();

        // Process enumerations with ID and label
        populateEnumLookups(lookupData);

        // Process yes/no type fields
        populateYesNoFields(lookupData);

        // Load data from repositories
        populateRepositoryData(lookupData);

        return lookupData;
    }

    /** Populates the lookup map with enumeration-based lookups */
    private void populateEnumLookups(Map<String, Object> lookupData) {
        // Map of enum class to its corresponding ID and label field names
        Map<Enum<?>[], EnumMapping> enumMappings = new HashMap<>();

        // Standard enum mappings
        enumMappings.put(
                AmenitiesType.values(), new EnumMapping("AmenitiesTypeID", "AmenitiesTypeName"));
        enumMappings.put(
                BuildSpecStatus.values(),
                new EnumMapping("BuildSpecStatusID", "BuildSpecStatusNames"));
        enumMappings.put(UseType.values(), new EnumMapping("UseTypeID", "UseTypeName"));
        enumMappings.put(ClassType.values(), new EnumMapping("ClassTypeID", "ClassTypeName"));
        enumMappings.put(ComplexType.values(), new EnumMapping("ComplexTypeID", "ComplexTypeName"));
        enumMappings.put(CondoType.values(), new EnumMapping("CondoTypeID", "CondoTypeName"));
        enumMappings.put(
                ConstructionStatus.values(),
                new EnumMapping("ConstructionStatusID", "ConstructionStatusName"));
        enumMappings.put(Features.values(), new EnumMapping("FeatureID", "FeatureName"));
        enumMappings.put(
                GovernmentInterest.values(),
                new EnumMapping("GovernmentInterestID", "GovernmentInterestName"));
        enumMappings.put(
                GreenStarRating.values(),
                new EnumMapping("GreenStarRatingID", "GreenStarRatingName"));
        enumMappings.put(HVACType.values(), new EnumMapping("HVACTypeID", "HVACTypeName"));
        enumMappings.put(SizeSource.values(), new EnumMapping("SizeSourceID", "SizeSourceName"));
        enumMappings.put(LandUse.values(), new EnumMapping("LandUseID", "LandUseName"));
        enumMappings.put(
                EnergyStarRating.values(),
                new EnumMapping("NABERSCertified", "EnergyStarRatingName"));
        enumMappings.put(
                WaterStarRating.values(),
                new EnumMapping("NABERSWaterCertified", "WaterStarRatingName"));
        enumMappings.put(OfficeHvac.values(), new EnumMapping("OfficeHVACID", "OfficeHVACName"));
        enumMappings.put(PowerType.values(), new EnumMapping("PowerTypeID", "PowerTypeName"));
        enumMappings.put(Prefix.values(), new EnumMapping("PrefixID", "PrefixName"));
        enumMappings.put(Quadrant.values(), new EnumMapping("QuadrantID", "QuadrantName"));
        enumMappings.put(RoofType.values(), new EnumMapping("RoofTypeID", "RoofTypeName"));
        enumMappings.put(
                SprinklerType.values(), new EnumMapping("SprinklerTypeID", "SprinklerTypeName"));
        enumMappings.put(Tenancy.values(), new EnumMapping("TenancyID", "TenancyName"));
        enumMappings.put(ZoningClass.values(), new EnumMapping("ZoningClassID", "ZoningClassName"));
        enumMappings.put(
                TypicalFloorPlate.values(),
                new EnumMapping("TypicalFloorPlateID", "TypicalFloorPlateName"));
        enumMappings.put(
                ConstructionType.values(),
                new EnumMapping("ConstructionTypeID", "ConstructionTypeName"));

        // Special cases with extra fields
        EnumMapping classTypeMapping = new EnumMapping("BuildingClassID", "ClassTypeName");
        classTypeMapping.addExtraField("UseTypeId", "getType");
        enumMappings.put(ClassType.values(), classTypeMapping);

        // Duplicate prefix for Prefix2ID
        lookupData.put(
                "Prefix2ID",
                mapEnumWithCustomKeys(Prefix.values(), "Prefix2ID", "PrefixName", Map.of()));

        // Process all enum mappings
        for (Map.Entry<Enum<?>[], EnumMapping> entry : enumMappings.entrySet()) {
            EnumMapping mapping = entry.getValue();
            lookupData.put(
                    mapping.idKey,
                    mapEnumWithCustomKeys(
                            entry.getKey(), mapping.idKey, mapping.labelKey, mapping.extraFields));
        }
    }

    /** Populates yes/no type fields in the lookup map */
    private void populateYesNoFields(Map<String, Object> lookupData) {
        List<String> yesNoFieldNames =
                Arrays.asList(
                        "AustraliaADA",
                        "CraneServed",
                        "HasPortAccess",
                        "HasReservedCoveredParking",
                        "HasRetailSqm",
                        "HasSurfaceParking",
                        "HasYard",
                        "HasYardFenced",
                        "isVented",
                        "OwnerOccupied",
                        "RailServed",
                        "Sprinklers",
                        "UseAddressAsPropertyName",
                        "YardPaved",
                        "HasSprinkler",
                        "Lifts",
                        "HVAC",
                        "Mezzanine",
                        "Awnings",
                        "HasYardUnfenced",
                        "IsFloodPlain",
                        "EarthquakeZoneID",
                        "HasUnreservedParkingSpaces");

        YesOrNoType[] yesNoValues = YesOrNoType.values();
        for (String fieldName : yesNoFieldNames) {
            lookupData.put(fieldName, mapEnumSingleField(yesNoValues, fieldName));
        }
    }

    /** Populates repository-based data in the lookup map */
    private void populateRepositoryData(Map<String, Object> lookupData) {
        // City data
        lookupData.put(
                "CityID",
                mapRepositoryData(
                        cityRepository.findByIsActiveTrueOrderByCityNameAsc(),
                        city ->
                                Map.of(
                                        "CityID", city.getCityId(),
                                        "CityName", city.getCityName(),
                                        "StateID", city.getStateId())));

        // County/Council data
        lookupData.put(
                "CouncilID",
                mapRepositoryData(
                        countyRepository.findByIsActiveTrueOrderByCountyNameAsc(),
                        county ->
                                Map.of(
                                        "CouncilID", county.getCountyId(),
                                        "CountyName", county.getCountyName())));
        //country
        Country country = countryRepository.findByCountryIdAndIsActiveTrue(countryId);
        CountryDTO countryDTO = countryMapper.toDto(country);
        lookupData.put("CountryID", List.of(countryDTO));

        // Street suffix data (reused for both suffix fields)
        List<Map<String, Object>> suffixData =
                mapRepositoryData(
                        suffixRepository.findByIsActive(true),
                        suffix ->
                                Map.of(
                                        "StreetSuffix1", suffix.getSuffixId(),
                                        "Suffix", suffix.getSuffix()));
        lookupData.put("StreetSuffix1", suffixData);
        lookupData.put("StreetSuffix2", suffixData);

        // State data
        lookupData.put(
                "StateID",
                mapRepositoryData(
                        stateRepository.findByIsActive(true),
                        state ->
                                Map.of(
                                        "StateID", state.getStateId(),
                                        "StateName", state.getStateName(),
                                        "StateAbbr", state.getStateAbbr(),
                                        "CountryID", state.getCountryId())));

        // Submarket data
        lookupData.put(
                "SubMarketID",
                mapRepositoryData(
                        subMarketRepository.findByIsActive(true),
                        subMarket ->
                                Map.of(
                                        "MarketID", subMarket.getMarketId(),
                                        "MarketName", subMarket.getMarketName(),
                                        "SubMarketID", subMarket.getSubMarketId(),
                                        "SubMarketName", subMarket.getSubMarketName())));

        // Zip code data
        lookupData.put(
                "ZipCode",
                mapRepositoryData(
                        zipCodeRepository.findByIsActive(true),
                        zip ->
                                Map.of(
                                        "ZipCode", zip.getZipCode(),
                                        "ZipCodeID", zip.getZipCodeId())));

        // Specific uses data
        lookupData.put(
                "SpecificUsesID",
                mapRepositoryData(
                        specificUsesRepository.findByIsActive(true),
                        use ->
                                Map.of(
                                        "SpecificUsesID", use.getSpecificUsesId(),
                                        "SpecificUsesName", use.getSpecificUsesName(),
                                        "UseTypeId", use.getUseTypeId())));

        // Market data
        lookupData.put(
                "MarketID",
                mapRepositoryData(
                        marketRepository.findByIsActive(true),
                        market ->
                                Map.of(
                                        "MarketID", market.getMarketId(),
                                        "MarketName", market.getMarketName(),
                                        "MetroID", market.getMetroId(),
                                        "PropertyType", market.getUseTypeId())));
    }

    /** Maps enum values to a list of maps with specified key names */
    private List<Map<String, Object>> mapEnumWithCustomKeys(
            Enum<?>[] enums, String idKey, String labelKey, Map<String, String> extraFields) {
        return Arrays.stream(enums)
                .map(
                        enumValue -> {
                            try {
                                Map<String, Object> valueMap = new LinkedHashMap<>();
                                Method getId = enumValue.getClass().getMethod("getId");
                                Method getLabel = enumValue.getClass().getMethod("getLabel");

                                valueMap.put(idKey, getId.invoke(enumValue));
                                valueMap.put(labelKey, getLabel.invoke(enumValue));
                                valueMap.put(
                                        enumValue.getClass().getSimpleName() + "Enum",
                                        enumValue.name());

                                for (var entry : extraFields.entrySet()) {
                                    Method extraMethod =
                                            enumValue.getClass().getMethod(entry.getValue());
                                    valueMap.put(entry.getKey(), extraMethod.invoke(enumValue));
                                }
                                return valueMap;
                            } catch (Exception ex) {
                                throw new RuntimeException(
                                        "Error mapping enum "
                                                + enumValue.getClass().getSimpleName(),
                                        ex);
                            }
                        })
                .collect(Collectors.toList());
    }

    /** Maps enum values to a list of maps with a single field */
    private List<Map<String, String>> mapEnumSingleField(Enum<?>[] enums, String fieldName) {
        return Arrays.stream(enums)
                .map(enumValue -> Map.of(fieldName, enumValue.name()))
                .collect(Collectors.toList());
    }

    /** Maps repository data using the provided mapper function */
    private <T> List<Map<String, Object>> mapRepositoryData(
            List<T> entityList, Function<T, Map<String, Object>> mapper) {
        return entityList.stream().map(mapper).collect(Collectors.toList());
    }

    /** Helper class to store enum mapping configuration */
    private static class EnumMapping {
        final String idKey;
        final String labelKey;
        final Map<String, String> extraFields;

        EnumMapping(String idKey, String labelKey) {
            this.idKey = idKey;
            this.labelKey = labelKey;
            this.extraFields = new HashMap<>();
        }

        void addExtraField(String fieldName, String methodName) {
            extraFields.put(fieldName, methodName);
        }
    }
}
