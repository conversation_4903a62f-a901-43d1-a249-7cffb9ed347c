package com.arealytics.core.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.domain.empiricalProd.PropertyStrataRelationship;
import com.arealytics.core.enumeration.CondoType;
import com.arealytics.core.repository.empiricalProd.PropertyRepository;
import com.arealytics.core.repository.empiricalProd.PropertyStrataRelationshipRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class MasterPropertyService {

  private final PropertyRepository propertyRepository;
  private final PropertyStrataRelationshipRepository propertyStrataRelationshipRepository;

  @Transactional
  public void processMasterStrataRelationships(Property property) {
    Integer propertyId = property.getPropertyID();
    CondoType condoType = property.getPropertyDetails().getCondoTypeID();

    if (condoType == CondoType.CHILD_FREEHOLD || condoType == CondoType.STRATA) {
      Optional<PropertyStrataRelationship> relationshipOpt = propertyStrataRelationshipRepository
          .findByStrataProperty_PropertyIDAndIsActive(
              propertyId, true);
      if (relationshipOpt.isPresent()) {
        Integer masterPropertyId = relationshipOpt.get().getMasterProperty().getPropertyID();
        recalculateMasterPropertySize(masterPropertyId);
      }
    }

    if (condoType == CondoType.MASTER_FREEHOLD || condoType == CondoType.MASTER_STRATA_RECORD) {
      List<PropertyStrataRelationship> relationshipOpt = propertyStrataRelationshipRepository
          .findByMasterProperty_PropertyIDAndIsActive(
              propertyId, true);
      if (!relationshipOpt.isEmpty()) {
        recalculateMasterPropertySize(propertyId);
      }
    }
  }

  // calculates the building size of a master property by summing the building
  // sizes of its active
  // child properties
  // and updates the master property with the new total size
  @Transactional
  public void recalculateMasterPropertySize(Integer masterPropertyId) {
    // Find all active child properties
    List<PropertyStrataRelationship> relationships = propertyStrataRelationshipRepository
        .findAllByMasterProperty_PropertyIDAndIsActive(
            masterPropertyId, true);

    // Sum buildingSF of all child properties
    BigDecimal totalSize = relationships.stream()
        .map(
            relationship -> propertyRepository.findById(
                relationship.getStrataProperty().getPropertyID()))
        .filter(Optional::isPresent)
        .map(Optional::get)
        .map(property -> property.getPropertySize().getBuildingSizeSF())
        .filter(size -> size != null)
        .reduce(BigDecimal.ZERO, BigDecimal::add);

    // Update master property
    propertyRepository
        .findById(masterPropertyId)
        .ifPresent(
            masterProperty -> {
              masterProperty.getPropertySize().setBuildingSizeSF(totalSize);
              propertyRepository.save(masterProperty);
            });
  }
}
