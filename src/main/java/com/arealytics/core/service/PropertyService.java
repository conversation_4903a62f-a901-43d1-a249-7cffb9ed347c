package com.arealytics.core.service;

import com.arealytics.core.dto.querydsl.PropertyDetailsQuerydslDTO;
import com.arealytics.core.dto.querydsl.PropertySearchQuerydslDTO;
import com.arealytics.core.dto.querydsl.PropertyMapSearchQuerydslDTO;
import com.arealytics.core.dto.request.PropertyMapSearchRequestDTO;
import com.arealytics.core.dto.request.PropertySearchRequestDTO;
import com.arealytics.core.dto.response.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.arealytics.core.enumeration.Features;
import com.arealytics.core.utils.EnumUtil;
import com.arealytics.core.utils.UnitConversionUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.core.domain.empiricalProd.Address;
import com.arealytics.core.domain.empiricalProd.Location;
import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.domain.empiricalProd.PropertyLocation;
import com.arealytics.core.enumeration.CondoType;
import com.arealytics.core.mapper.PropertyMapper;
import com.arealytics.core.repository.empiricalProd.PropertyRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class PropertyService {

    private final PropertyRepository propertyRepository;

    private final LocationService locationService;
    private final AddressService addressService;
    private final PropertyResearchStatusService propertyResearchStatusService;
    private final PropertyUseService propertyUseService;
    private final PropertyAmenitiesService propertyAmenitiesService;
    private final PropertyStrataService propertyStrataService;

    private final PropertyMapper propertyMapper;

    @Transactional
    public PropertyResponseDTO createProperty(PropertyDetailsDTO propertyDTO) {

        Property property = propertyMapper.toEntity(propertyDTO, new Property());
        property.setIsActive(true);

        // Create and save location entity
        Location savedLocation = locationService.createLocation(propertyDTO);

        if (property.getPropertyLocation() == null) {
            property.setPropertyLocation(new PropertyLocation());
        }

        // Set the location reference
        property.getPropertyLocation().setLocation(savedLocation);

        // Save property to a database to generate ID
        Property savedProperty = propertyRepository.save(property);

        // Create and save address, then link it to the property location
        Address savedAddress =
                addressService.createAddress(
                        propertyDTO, savedLocation.getLocationID(), savedProperty.getPropertyID());
        property.getPropertyLocation().setAddressId(savedAddress);

        // Set use type and specific use details and create use record
        propertyUseService.setUseDetailsForCreate(propertyDTO, savedProperty);
        propertyUseService.saveUseRecord(propertyDTO, savedProperty);

        // Save property research status
        if (propertyDTO.getResearchTypeID() != null) {
            propertyResearchStatusService.savePropertyResearchStatus(
                    savedProperty.getPropertyID(),
                    propertyDTO.getResearchTypeID(),
                    null);
        }

        // Save amenities
        propertyAmenitiesService.savePropertyAmenities(
                savedProperty.getPropertyID(), propertyDTO.getAmenities());

        // Handle strata relationship
        propertyStrataService.handleRelationship(savedProperty.getPropertyID(), propertyDTO.getMasterPropertyId(), true, null);

        return propertyMapper.toDto(savedProperty);
    }


    @Transactional
    public PropertyResponseDTO updateProperty(PropertyDetailsDTO propertyDTO) {

        // Validate input
        if (propertyDTO.getPropertyID() == null) {
            throw new IllegalArgumentException("Property ID must not be null for update");
        }

        // Retrieve existing property or throw exception if not found
        Property existingProperty =
                propertyRepository
                        .findById(propertyDTO.getPropertyID())
                        .orElseThrow(
                                () ->
                                        new RuntimeException(
                                                "Property not found with ID: "
                                                        + propertyDTO.getPropertyID()));

        // Map DTO to entity while preserving ID and active status
        Property updatedProperty = propertyMapper.toEntity(propertyDTO, existingProperty);
        updatedProperty.setPropertyID(existingProperty.getPropertyID());
        updatedProperty.setIsActive(true);

        // SECTION: Handle location updates
        updatePropertyLocation(propertyDTO, existingProperty, updatedProperty);

        // SECTION: Handle use types and specific uses
        propertyUseService.setUseDetailsForUpdate(propertyDTO, existingProperty, updatedProperty);

        // Save updated property
        Property savedProperty = propertyRepository.save(updatedProperty);

        // Create or update Use record if useType and specific use are provided
        propertyUseService.saveUseRecord(propertyDTO, savedProperty);

         // Save property research status
        if (propertyDTO.getResearchTypeID() != null) {
            propertyResearchStatusService.savePropertyResearchStatus(
                    savedProperty.getPropertyID(),
                    propertyDTO.getResearchTypeID(),
                    null);
        }

        // Save amenities
        propertyAmenitiesService.savePropertyAmenities(
        savedProperty.getPropertyID(), propertyDTO.getAmenities());

        // Handle Strata relationship
        propertyStrataService.handleRelationship(propertyDTO.getPropertyID(), propertyDTO.getMasterPropertyId(), true, null);

        return propertyMapper.toDto(savedProperty);
    }

    private void updatePropertyLocation(
            PropertyDetailsDTO propertyDTO, Property existingProperty, Property updatedProperty) {
        // Initialize PropertyLocation if needed
        if (updatedProperty.getPropertyLocation() == null) {
            updatedProperty.setPropertyLocation(new PropertyLocation());
        }

        // Handle Location entity
        if (existingProperty.getPropertyLocation() != null
                && existingProperty.getPropertyLocation().getLocation() != null) {
            // Update existing location
            Location savedLocation =
                    locationService.updateLocation(
                            propertyDTO,
                            existingProperty.getPropertyLocation().getLocation().getLocationID());
            updatedProperty.getPropertyLocation().setLocation(savedLocation);
        } else if (propertyDTO.getLocationFields().getLatitude() != null && propertyDTO.getLocationFields().getLongitude() != null) {
            // Create new location
            Location savedLocation = locationService.createLocation(propertyDTO);
            updatedProperty.getPropertyLocation().setLocation(savedLocation);
        }

        // Handle Address entity
        if (updatedProperty.getPropertyLocation().getLocation() != null) {
            Integer locationId = updatedProperty.getPropertyLocation().getLocation().getLocationID();

            if (existingProperty.getPropertyLocation() != null
                    && existingProperty.getPropertyLocation().getAddressId() != null) {
                // Update existing address
                Address savedAddress =
                        addressService.updateAddress(
                                propertyDTO, locationId, propertyDTO.getPropertyID());
                updatedProperty.getPropertyLocation().setAddressId(savedAddress);
            } else {
                // Create new address
                Address savedAddress =
                        addressService.createAddress(
                                propertyDTO, locationId, propertyDTO.getPropertyID());
                updatedProperty.getPropertyLocation().setAddressId(savedAddress);
            }
        }
    }

    @Transactional("empiricalProdTransactionManager")
    public PropertyDetailsSizeResponseDTO findPropertyDetailsByPropertyID(Integer propertyId) {
        PropertyDetailsQuerydslDTO propertyDetails = propertyRepository.findPropertyDetailsByPropertyID(propertyId);
        PropertyDetailsSizeResponseDTO response = new PropertyDetailsSizeResponseDTO();
        BeanUtils.copyProperties(propertyDetails, response);
        if(propertyDetails.getLotSizeSF() != null) {
            BigDecimal lotSizeSM = BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getLotSizeSF().doubleValue()));
            response.setLotSizeSM(lotSizeSM);
            response.setLotSizeSMFormatted(lotSizeSM.setScale(0, RoundingMode.HALF_UP).toPlainString());
        }
        if(propertyDetails.getLotSizeAC() != null) {
            response.setLotSizeACSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getLotSizeAC().doubleValue())));
        }
        if(propertyDetails.getBuildingSizeSF() != null) {
            BigDecimal buildingSizeSM = BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getBuildingSizeSF().doubleValue()));
            response.setBuildingSizeSM(buildingSizeSM);
            response.setBuildingSizeSMFormatted(buildingSizeSM.setScale(0, RoundingMode.HALF_UP).toPlainString());
        }
        if(propertyDetails.getContributedGBASizeSF() != null) {
            response.setContributedGBASM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getContributedGBASizeSF().doubleValue())));
        }
        if(propertyDetails.getGlaSizeSF() != null) {
            response.setGlaSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getGlaSizeSF().doubleValue())));
        }
        if(propertyDetails.getGlarSizeSF() != null) {
            response.setGlarSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getGlarSizeSF().doubleValue())));
        }
        if(propertyDetails.getMezzanineSizeSF() != null) {
            response.setMezzanineSizeSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getMezzanineSizeSF().doubleValue())));
        }
        if(propertyDetails.getAwningsSizeSF() != null) {
            response.setAwningsSizeSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getAwningsSizeSF().doubleValue())));
        }
        if(propertyDetails.getRetailSize() != null) {
            response.setRetailSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getRetailSize().doubleValue())));
        }
        if(propertyDetails.getOfficeSize() != null) {
            response.setOfficeSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getOfficeSize().doubleValue())));
        }
        if(propertyDetails.getNla() != null) {
            response.setNlaSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getNla().doubleValue())));
        }
        if(propertyDetails.getClearHeightMin() != null) {
            response.setClearHeightMinM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getClearHeightMin().doubleValue())));
        }
        if(propertyDetails.getClearHeightMax() != null) {
            response.setClearHeightMaxM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getClearHeightMax().doubleValue())));
        }
        if(propertyDetails.getTypicalFloorSize() != null) {
            response.setTypicalFloorSizeSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getTypicalFloorSize().doubleValue())));
        }
        if(propertyDetails.getDepth() != null) {
            response.setDepthM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getDepth().doubleValue())));
        }
        if(propertyDetails.getWidth() != null) {
            response.setWidthM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getWidth().doubleValue())));
        }
        if(propertyDetails.getSmallestFloor() != null) {
            response.setSmallestFloorSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getSmallestFloor().doubleValue())));
        }
        if(propertyDetails.getLargestFloor() != null) {
            response.setLargestFloorSM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getLargestFloor().doubleValue())));
        }
        if(propertyDetails.getTotalAnchor() != null) {
            response.setTotalAnchorSF(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getTotalAnchor().doubleValue())));
        }
        if(propertyDetails.getRetailFrontage() != null) {
            response.setRetailFrontageM(BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(propertyDetails.getRetailFrontage().doubleValue())));
        }
        if(propertyDetails.getFeatureIDs() != null) {
            response.setFeatureIDs(EnumUtil.getEnumValuesFromIds(propertyDetails.getFeatureIDs(), Features.class, false));
        }
        return response;

    }

    // Utility method to check if property is of MASTER_FREEHOLD type
    public boolean isMasterFreehold(Property property) {
      return Objects.equals(property.getPropertyDetails().getCondoTypeID(), CondoType.MASTER_FREEHOLD);
    }

    @Transactional("empiricalProdTransactionManager")
    public List<PropertySearchSizeResponseDTO> findPropertiesBySearch(PropertySearchRequestDTO searchCriteria) {

        int page = searchCriteria.getPage() != null ? searchCriteria.getPage() : 0;
        int pageSize = searchCriteria.getPageSize() != null ? searchCriteria.getPageSize() : 100;

        Pageable pageable = PageRequest.of(page, pageSize);

        List<PropertySearchQuerydslDTO> results = propertyRepository.findPropertiesBySearch(searchCriteria, pageable);
        List<PropertySearchSizeResponseDTO> responseList = new ArrayList<>();

        for (PropertySearchQuerydslDTO dto : results) {
            PropertySearchSizeResponseDTO response = new PropertySearchSizeResponseDTO();
            BeanUtils.copyProperties(dto, response);
            if(dto.getLotSizeSF() != null) {
                BigDecimal lotSizeSM = BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(dto.getLotSizeSF().doubleValue()));
                response.setLotSizeSM(lotSizeSM);
                response.setLotSizeSMFormatted(lotSizeSM.setScale(0, RoundingMode.HALF_UP).toPlainString());
            }
            if(dto.getBuildingSizeSF() != null) {
                BigDecimal buildingSizeSM = BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(dto.getBuildingSizeSF().doubleValue()));
                response.setBuildingSizeSM(buildingSizeSM);
                response.setBuildingSizeSMFormatted(buildingSizeSM.setScale(0, RoundingMode.HALF_UP).toPlainString());
            }
            if(dto.getContributedGBASizeSF() != null) {
                BigDecimal contributedGBASizeSM = BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(dto.getContributedGBASizeSF().doubleValue()));
                response.setContributedGBASizeSM(contributedGBASizeSM);
                response.setContributedGBASizeSMFormatted(contributedGBASizeSM.setScale(0, RoundingMode.HALF_UP).toPlainString());
            }
            if (searchCriteria.getIncludeStrataPropertiesForMaster() != null && (dto.getCondoTypeID() == CondoType.MASTER_STRATA_RECORD || dto.getCondoTypeID() == CondoType.MASTER_FREEHOLD)) {
                List<PropertyDTO> strataProperties = propertyRepository.findStrataPropertiesByMasterStrataProperty(dto.getPropertyID());
                response.setStrataProperties(strataProperties);
            }
            responseList.add(response);
        }

        return responseList;
    }

    @Transactional("empiricalProdTransactionManager")
    public Long findCountPropertiesBySearch(PropertySearchRequestDTO searchCriteria) {

        return propertyRepository.countBySearchCriteria(searchCriteria);
    }

    @Transactional("empiricalProdTransactionManager")
    public List<PropertyMapSearchSizeResponseDTO> findPropertiesByMapSearch(PropertyMapSearchRequestDTO searchCriteria) {
        int page = searchCriteria.getPage() != null ? searchCriteria.getPage() : 0;
        int pageSize = searchCriteria.getPageSize() != null ? searchCriteria.getPageSize() : 100;

        Pageable pageable = PageRequest.of(page, pageSize);

        List<PropertyMapSearchQuerydslDTO> results = propertyRepository.findPropertiesByMapSearch(searchCriteria, pageable);
        List<PropertyMapSearchSizeResponseDTO> responseList = new ArrayList<>();

        for (PropertyMapSearchQuerydslDTO dto : results) {
            PropertyMapSearchSizeResponseDTO response = new PropertyMapSearchSizeResponseDTO();
            BeanUtils.copyProperties(dto, response);
            if(dto.getLotSizeSF() != null) {
                BigDecimal lotSizeSM = BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(dto.getLotSizeSF().doubleValue()));
                response.setLotSizeSM(lotSizeSM);
                response.setLotSizeSMFormatted(lotSizeSM.setScale(0, RoundingMode.HALF_UP).toPlainString());
            }
            if(dto.getBuildingSizeSF() != null) {
                BigDecimal buildingSizeSM = BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(dto.getBuildingSizeSF().doubleValue()));
                response.setBuildingSizeSM(buildingSizeSM);
                response.setBuildingSizeSMFormatted(buildingSizeSM.setScale(0, RoundingMode.HALF_UP).toPlainString());
            }
            if(dto.getContributedGBASizeSF() != null) {
                BigDecimal contributedGBASizeSM = BigDecimal.valueOf(UnitConversionUtil.sqftToSqm(dto.getContributedGBASizeSF().doubleValue()));
                response.setContributedGBASizeSM(contributedGBASizeSM);
                response.setContributedGBASizeSMFormatted(contributedGBASizeSM.setScale(0, RoundingMode.HALF_UP).toPlainString());
            }
            responseList.add(response);
        }

        return responseList;
    }

    @Transactional("empiricalProdTransactionManager")
    public Long findCountPropertiesByMapSearch(PropertyMapSearchRequestDTO searchCriteria) {

        return propertyRepository.countByMapSearchCriteria(searchCriteria);
    }
}
