package com.arealytics.core.service;

import com.arealytics.core.dto.response.PropertyDetailsResponseDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.core.domain.empiricalProd.Address;
import com.arealytics.core.domain.empiricalProd.Location;
import com.arealytics.core.domain.empiricalProd.Property;
import com.arealytics.core.domain.empiricalProd.PropertyLocation;
import com.arealytics.core.dto.request.PropertyDTO;
import com.arealytics.core.dto.response.PropertyResponseDTO;
import com.arealytics.core.mapper.PropertyMapper;
import com.arealytics.core.repository.empiricalProd.PropertyRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class PropertyService {

    private final PropertyRepository propertyRepository;

    private final LocationService locationService;
    private final AddressService addressService;
    private final PropertyResearchStatusService propertyResearchStatusService;
    private final PropertyUseService propertyUseService;
    private final PropertyAmenitiesService propertyAmenitiesService;
    private final MasterPropertyService masterPropertyService;

    private final PropertyMapper propertyMapper;

    @Transactional
    public PropertyResponseDTO createProperty(PropertyDTO propertyDTO) {

        Property property = propertyMapper.toEntity(propertyDTO, new Property());
        property.setIsActive(true);

        // Create and save location entity
        Location savedLocation = locationService.createLocation(propertyDTO);

        if (property.getLocation() == null) {
            property.setLocation(new PropertyLocation());
        }

        // Set the location reference
        property.getLocation().setLocation(savedLocation);

        // Save property to database to generate ID
        Property savedProperty = propertyRepository.save(property);

        // Create and save address, then link it to the property location
        Address savedAddress =
                addressService.createAddress(
                        propertyDTO, savedLocation.getLocationID(), savedProperty.getPropertyID());
        property.getLocation().setAddressId(savedAddress);

        // Set use type and specific use details and create use record
        propertyUseService.setUseDetailsForCreate(propertyDTO, savedProperty);
        propertyUseService.saveUseRecord(propertyDTO, savedProperty);

        // Create default property research status
        propertyResearchStatusService.createDefaultResearchStatus(savedProperty);

        // Save amenities
        propertyAmenitiesService.savePropertyAmenities(
                savedProperty.getPropertyID(), propertyDTO.getAmenities(), propertyDTO.getEntityId());

        return propertyMapper.toDto(savedProperty);
    }


    @Transactional
    public PropertyResponseDTO updateProperty(PropertyDTO propertyDTO) {

        // Validate input
        if (propertyDTO.getPropertyID() == null) {
            throw new IllegalArgumentException("Property ID must not be null for update");
        }

        // Retrieve existing property or throw exception if not found
        Property existingProperty =
                propertyRepository
                        .findById(propertyDTO.getPropertyID())
                        .orElseThrow(
                                () ->
                                        new RuntimeException(
                                                "Property not found with ID: "
                                                        + propertyDTO.getPropertyID()));

        // Map DTO to entity while preserving ID and active status
        Property updatedProperty = propertyMapper.toEntity(propertyDTO, existingProperty);
        updatedProperty.setPropertyID(existingProperty.getPropertyID());
        updatedProperty.setIsActive(true);

        // SECTION: Handle location updates
        updatePropertyLocation(propertyDTO, existingProperty, updatedProperty);

        // SECTION: Handle use types and specific uses
        propertyUseService.setUseDetailsForUpdate(propertyDTO, existingProperty, updatedProperty);

        // Save updated property
        Property savedProperty = propertyRepository.save(updatedProperty);

        // Create or update Use record if useType and specific use are provided
        propertyUseService.saveUseRecord(propertyDTO, savedProperty);

        // Save amenities
        propertyAmenitiesService.savePropertyAmenities(
                savedProperty.getPropertyID(), propertyDTO.getAmenities(), propertyDTO.getEntityId());

        // Process strata relationships and update master property size
        masterPropertyService.processMasterStrataRelationships(savedProperty);

        return propertyMapper.toDto(savedProperty);
    }

    private void updatePropertyLocation(
            PropertyDTO propertyDTO, Property existingProperty, Property updatedProperty) {
        // Initialize PropertyLocation if needed
        if (updatedProperty.getLocation() == null) {
            updatedProperty.setLocation(new PropertyLocation());
        }

        // Handle Location entity
        if (existingProperty.getLocation() != null
                && existingProperty.getLocation().getLocation() != null) {
            // Update existing location
            Location savedLocation =
                    locationService.updateLocation(
                            propertyDTO,
                            existingProperty.getLocation().getLocation().getLocationID());
            updatedProperty.getLocation().setLocation(savedLocation);
        } else if (propertyDTO.getLatitude() != null && propertyDTO.getLongitude() != null) {
            // Create new location
            Location savedLocation = locationService.createLocation(propertyDTO);
            updatedProperty.getLocation().setLocation(savedLocation);
        }

        // Handle Address entity
        if (updatedProperty.getLocation().getLocation() != null) {
            Integer locationId = updatedProperty.getLocation().getLocation().getLocationID();

            if (existingProperty.getLocation() != null
                    && existingProperty.getLocation().getAddressId() != null) {
                // Update existing address
                Address savedAddress =
                        addressService.updateAddress(
                                propertyDTO, locationId, propertyDTO.getPropertyID());
                updatedProperty.getLocation().setAddressId(savedAddress);
            } else {
                // Create new address
                Address savedAddress =
                        addressService.createAddress(
                                propertyDTO, locationId, propertyDTO.getPropertyID());
                updatedProperty.getLocation().setAddressId(savedAddress);
            }
        }
    }

    @Transactional("empiricalProdTransactionManager")
    public PropertyDetailsResponseDTO findPropertyDetailsByPropertyID(Integer propertyId) {
        return propertyRepository.findPropertyDetailsByPropertyID(propertyId);

    }
}
