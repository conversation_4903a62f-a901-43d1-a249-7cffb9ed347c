spring.application.name=apicore

# Enable JPA Logging
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=none

# Database Configuration
spring.datasource.empirical-prod.jdbc-url=${SPRING_DATASOURCE_URL}
spring.datasource.empirical-prod.username=${SPRING_DATASOURCE_USERNAME}
spring.datasource.empirical-prod.password=${SPRING_DATASOURCE_PASSWORD}
spring.datasource.empirical-prod.driver-class-name=com.mysql.cj.jdbc.Driver

spring.datasource.empirical-gis.jdbc-url=*************************************************************************************************************************************
spring.datasource.empirical-gis.username=dbuser-arealytics-app
spring.datasource.empirical-gis.password=DBUserArealyticsAapp#622
spring.datasource.empirical-gis.driver-class-name=com.mysql.cj.jdbc.Driver

spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

#CORS Configration
cors.allowed-origins=http://localhost:4200,http://localhost:4400,http://localhost:4500,http://localhost:8080,https://*.arealytics.com.au

#swagger details
swagger.server.url=https://api-phoenix.arealytics.com.au
swagger.server.description=Development Server
