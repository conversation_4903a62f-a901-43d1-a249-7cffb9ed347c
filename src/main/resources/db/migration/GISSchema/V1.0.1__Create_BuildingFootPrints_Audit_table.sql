CREATE TABLE IF NOT EXISTS Empirical_GIS.BuildingFootPrints_Audit (
    BuildingFootPrintID INT NOT NULL,
    revision_id INT NOT NULL,
    revision_type TINYINT DEFAULT NULL,
    AL_Building_ID INT DEFAULT NULL,
    AL_Building_Id_MOD BIT DEFAULT NULL,
    AdditionalSpecificUseTypeId INT DEFAULT NULL,
    additionalSpecificUseTypeId_MOD BIT DEFAULT NULL,
    AdditionalUseTypeId INT DEFAULT NULL,
    additionalUseTypeId_MOD BIT DEFAULT NULL,
    CRE_PropertyID INT DEFAULT NULL,
    crePropertyId_MOD BIT DEFAULT NULL,
    Description TEXT DEFAULT NULL,
    description_MOD BIT DEFAULT NULL,
    Floors INT DEFAULT NULL,
    floors_MOD BIT DEFAULT NULL,
    isActive TINYINT DEFAULT NULL,
    isActive_MOD BIT DEFAULT NULL,
    IsD<PERSON><PERSON> BIT DEFAULT NULL,
    isDefault_MOD BIT DEFAULT NULL,
    MainSpecificUseTypeId INT DEFAULT NULL,
    mainSpecificUseTypeId_MOD BIT DEFAULT NULL,
    PropertyMaxFloor INT DEFAULT NULL,
    propertyMaxFloor_MOD BIT DEFAULT NULL,
    PropertyMinFloor INT DEFAULT NULL,
    propertyMinFloor_MOD BIT DEFAULT NULL,
    Shape GEOMETRY DEFAULT NULL,
    shape_MOD BIT DEFAULT NULL,
    SizeInSF FLOAT(53) DEFAULT NULL,
    sizeInSF_MOD BIT DEFAULT NULL,
    UseTypeId INT DEFAULT NULL,
    useTypeId_MOD BIT DEFAULT NULL,
    PRIMARY KEY (BuildingFootPrintID, revision_id),
    CONSTRAINT FK_BuildingFootPrintsAudit_RevisionInfo FOREIGN KEY (revision_id) REFERENCES Revision_Info (id)
);
