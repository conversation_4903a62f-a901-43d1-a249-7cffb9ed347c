CREATE TABLE IF NOT EXISTS `Empirical_Prod`.`Pa<PERSON><PERSON>_Audit` (
    `<PERSON><PERSON><PERSON>ID` INT NOT NULL,
    `revision_id` INT NOT NULL,
    `revision_type` TINYINT DEFAULT NULL,
    `Block` VARCHAR(100) DEFAULT NULL,
    `block_MOD` BIT DEFAULT NULL,
    `IsActive` TINYINT(1) DEFAULT NULL,
    `isActive_MOD` BIT DEFAULT NULL,
    `Lot` VARCHAR(100) DEFAULT NULL,
    `lot_MOD` BIT DEFAULT NULL,
    `ParcelNo` VARCHAR(100) DEFAULT NULL,
    `parcelNo_MOD` BIT DEFAULT NULL,
    `ParcelNoFix` VARCHAR(100) DEFAULT NULL,
    `parcelNoFix_MOD` BIT DEFAULT NULL,
    `ParcelSize` FLOAT(53) DEFAULT NULL,
    `parcelSize_MOD` BIT DEFAULT NULL,
    `Plan` VARCHAR(255) DEFAULT NULL,
    `plan_MOD` BIT DEFAULT NULL,
    `Plan Type` VARCHAR(255) DEFAULT NULL,
    `planType_MOD` BIT DEFAULT NULL,
    `Section` VARCHAR(255) DEFAULT NULL,
    `section_MOD` BIT DEFAULT NULL,
    `SubDivision` VARCHAR(100) DEFAULT NULL,
    `subDivision_MOD` BIT DEFAULT NULL,
    PRIMARY KEY (`ParcelID`, `revision_id`),
    CONSTRAINT `FK_ParcelAudit_RevisionInfo` FOREIGN KEY (`revision_id`) REFERENCES `Revision_Info` (`id`)
) ;
