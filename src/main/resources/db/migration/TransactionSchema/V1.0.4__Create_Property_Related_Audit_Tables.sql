CREATE TABLE IF NOT EXISTS `Empirical_Prod`.`Address_Audit` (
  `AddressID` int NOT NULL,
  `revision_id` int NOT NULL,
  `revision_type` tinyint DEFAULT NULL,
  `Address1` varchar(200) DEFAULT NULL,
  `address1_MOD` bit(1) DEFAULT NULL,
  `Address2` varchar(50) DEFAULT NULL,
  `address2_MOD` bit(1) DEFAULT NULL,
  `AddressStreetName` varchar(45) DEFAULT NULL,
  `addressStreetName_MOD` bit(1) DEFAULT NULL,
  `AddressStreetNumber` varchar(45) DEFAULT NULL,
  `addressStreetNumber_MOD` bit(1) DEFAULT NULL,
  `AddressText` varchar(255) DEFAULT NULL,
  `addressText_MOD` bit(1) DEFAULT NULL,
  `AddressTypeID` int DEFAULT NULL,
  `addressTypeId_MOD` bit(1) DEFAULT NULL,
  `BuildingNumber` varchar(45) DEFAULT NULL,
  `buildingNumber_MOD` bit(1) DEFAULT NULL,
  `CityID` int DEFAULT NULL,
  `cityId_MOD` bit(1) DEFAULT NULL,
  `ComplexName` varchar(100) DEFAULT NULL,
  `complexName_MOD` bit(1) DEFAULT NULL,
  `CountryID` int DEFAULT NULL,
  `countryId_MOD` bit(1) DEFAULT NULL,
  `CountyID` int DEFAULT NULL,
  `countyId_MOD` bit(1) DEFAULT NULL,
  `EastWestSt` varchar(100) DEFAULT NULL,
  `eastWestSt_MOD` bit(1) DEFAULT NULL,
  `FloorNumber` varchar(5) DEFAULT NULL,
  `floorNumber_MOD` bit(1) DEFAULT NULL,
  `IsActive` bit(1) DEFAULT NULL,
  `isActive_MOD` bit(1) DEFAULT NULL,
  `IsIntersection` bit(1) DEFAULT NULL,
  `isIntersection_MOD` bit(1) DEFAULT NULL,
  `LocationID` int DEFAULT NULL,
  `locationId_MOD` bit(1) DEFAULT NULL,
  `MarketStateID` int DEFAULT NULL,
  `marketStateId_MOD` bit(1) DEFAULT NULL,
  `NorthSouthSt` varchar(100) DEFAULT NULL,
  `northSouthSt_MOD` bit(1) DEFAULT NULL,
  `ParentID` int DEFAULT NULL,
  `parentId_MOD` bit(1) DEFAULT NULL,
  `ParentTableID` int DEFAULT NULL,
  `parentTableId_MOD` bit(1) DEFAULT NULL,
  `PartOfCenterComplex` int DEFAULT NULL,
  `partOfCenterComplex_MOD` bit(1) DEFAULT NULL,
  `Prefix2ID` int DEFAULT NULL,
  `prefix2Id_MOD` bit(1) DEFAULT NULL,
  `PrefixID` int DEFAULT NULL,
  `prefixId_MOD` bit(1) DEFAULT NULL,
  `PrimaryAccess` varchar(100) DEFAULT NULL,
  `primaryAccess_MOD` bit(1) DEFAULT NULL,
  `PrimaryFrontage` varchar(20) DEFAULT NULL,
  `primaryFrontage_MOD` bit(1) DEFAULT NULL,
  `PrimaryStreet` varchar(50) DEFAULT NULL,
  `primaryStreet_MOD` bit(1) DEFAULT NULL,
  `PrimaryTrafficCount` varchar(10) DEFAULT NULL,
  `primaryTrafficCount_MOD` bit(1) DEFAULT NULL,
  `PrimaryTrafficCountDate` datetime(6) DEFAULT NULL,
  `primaryTrafficCountDate_MOD` bit(1) DEFAULT NULL,
  `QuadrantID` int DEFAULT NULL,
  `quadrantId_MOD` bit(1) DEFAULT NULL,
  `SecondaryAccess` varchar(100) DEFAULT NULL,
  `secondaryAccess_MOD` bit(1) DEFAULT NULL,
  `SecondaryFrontage` varchar(20) DEFAULT NULL,
  `secondaryFrontage_MOD` bit(1) DEFAULT NULL,
  `SecondaryStreet` varchar(50) DEFAULT NULL,
  `secondaryStreet_MOD` bit(1) DEFAULT NULL,
  `SecondaryTrafficCount` varchar(10) DEFAULT NULL,
  `secondaryTrafficCount_MOD` bit(1) DEFAULT NULL,
  `SecondaryTrafficCountDate` datetime(6) DEFAULT NULL,
  `secondaryTrafficCountDate_MOD` bit(1) DEFAULT NULL,
  `Sequence` int DEFAULT NULL,
  `sequence_MOD` bit(1) DEFAULT NULL,
  `StateID` int DEFAULT NULL,
  `stateId_MOD` bit(1) DEFAULT NULL,
  `StreetNumberMax` varchar(20) DEFAULT NULL,
  `streetNumberMax_MOD` bit(1) DEFAULT NULL,
  `StreetNumberMaxN` int DEFAULT NULL,
  `streetNumberMaxN_MOD` bit(1) DEFAULT NULL,
  `StreetNumberMin` varchar(20) DEFAULT NULL,
  `streetNumberMin_MOD` bit(1) DEFAULT NULL,
  `StreetNumberMinN` int DEFAULT NULL,
  `streetNumberMinN_MOD` bit(1) DEFAULT NULL,
  `Suffix2ID` int DEFAULT NULL,
  `suffix2Id_MOD` bit(1) DEFAULT NULL,
  `SuffixID` int DEFAULT NULL,
  `suffixId_MOD` bit(1) DEFAULT NULL,
  `Zip4` varchar(4) DEFAULT NULL,
  `zip4_MOD` bit(1) DEFAULT NULL,
  `ZipCode` varchar(10) DEFAULT NULL,
  `zipCode_MOD` bit(1) DEFAULT NULL,
  `location_MOD` bit(1) DEFAULT NULL,
  `property_MOD` bit(1) DEFAULT NULL,
  PRIMARY KEY (`AddressID`,`revision_id`),
  CONSTRAINT `FK_AddressAudit_RevisionInfo` FOREIGN KEY (`revision_id`) REFERENCES `Revision_Info` (`id`)
) ;

CREATE TABLE IF NOT EXISTS `Empirical_Prod`.`Location_Audit` (
  `LocationID` int NOT NULL,
  `revision_id` int NOT NULL,
  `revision_type` tinyint DEFAULT NULL,
  `GISShapeID` int DEFAULT NULL,
  `gisShapeID_MOD` bit(1) DEFAULT NULL,
  `Latitude` decimal(21,14) DEFAULT NULL,
  `latitude_MOD` bit(1) DEFAULT NULL,
  `LocationPoint` point DEFAULT NULL,
  `locationPoint_MOD` bit(1) DEFAULT NULL,
  `Longitude` decimal(21,14) DEFAULT NULL,
  `longitude_MOD` bit(1) DEFAULT NULL,
  `RoooftopSourceID` int DEFAULT NULL,
  `rooftopSourceID_MOD` bit(1) DEFAULT NULL,
  `ZCoordinate` decimal(15,8) DEFAULT NULL,
  `zCoordinate_MOD` bit(1) DEFAULT NULL,
  `addresses_MOD` bit(1) DEFAULT NULL,
  PRIMARY KEY (`LocationID`,`revision_id`),
  CONSTRAINT `FK_LocationAudit_RevisionInfo` FOREIGN KEY (`revision_id`) REFERENCES `Revision_Info` (`id`)
);

CREATE TABLE IF NOT EXISTS `Empirical_Prod`.`Property_Audit` (
  `PropertyID` int NOT NULL,
  `revision_id` int NOT NULL,
  `revision_type` tinyint DEFAULT NULL,
  `Adding` bit(1) DEFAULT NULL,
  `Amenities` text,
  `Amps` int DEFAULT NULL,
  `BayDepth` decimal(13,2) DEFAULT NULL,
  `BayWidth` decimal(13,2) DEFAULT NULL,
  `BuildSpecStatusID` int DEFAULT NULL,
  `Depth` decimal(10,2) DEFAULT NULL,
  `DLCount` int DEFAULT NULL,
  `DockHigh` int DEFAULT NULL,
  `FreighElevators` int DEFAULT NULL,
  `GradeLevelIn` int DEFAULT NULL,
  `HasPortAccess` bit(1) DEFAULT NULL,
  `HasResCoveredParking` bit(1) DEFAULT NULL,
  `HasReservedParkingSpaces` bit(1) DEFAULT NULL,
  `HasSprinkler` bit(1) DEFAULT NULL,
  `HasUnreservedParkingSpaces` bit(1) DEFAULT NULL,
  `HasYard` bit(1) DEFAULT NULL,
  `HasYardFenced` bit(1) DEFAULT NULL,
  `HasYardUnfenced` bit(1) DEFAULT NULL,
  `HVAC` bit(1) DEFAULT NULL,
  `IncludeInAnalytics` bit(1) DEFAULT NULL,
  `InternalComments` text,
  `IsFloodPlain` bit(1) DEFAULT NULL,
  `LCount` int DEFAULT NULL,
  `Lifts` bit(1) DEFAULT NULL,
  `LiftsCount` int DEFAULT NULL,
  `LLCount` int DEFAULT NULL,
  `NoOfAnchor` int DEFAULT NULL,
  `NoOfUnits` int DEFAULT NULL,
  `OfficeAC` int DEFAULT NULL,
  `ParkingElevators` int DEFAULT NULL,
  `ParkingRatio` varchar(25) DEFAULT NULL,
  `ParkingSpaces` int DEFAULT NULL,
  `PassengerElevators` int DEFAULT NULL,
  `Phase` int DEFAULT NULL,
  `PotentialZoningID` int DEFAULT NULL,
  `PowerComments` varchar(2000) DEFAULT NULL,
  `PowerType` int DEFAULT NULL,
  `PropertyComments` text,
  `RailServed` bit(1) DEFAULT NULL,
  `ReservedParkingSpaces` int DEFAULT NULL,
  `ReservedParkingSpacesRatePerMonth` decimal(10,2) DEFAULT NULL,
  `SLCount` int DEFAULT NULL,
  `TotalAnchor` decimal(12,3) DEFAULT NULL,
  `TruckWell` int DEFAULT NULL,
  `UnreservedParkingSpaces` int DEFAULT NULL,
  `UnreservedParkingSpacesRatePerMonth` decimal(10,2) DEFAULT NULL,
  `UtilityComments` text,
  `Vacancy` int DEFAULT NULL,
  `Volts` int DEFAULT NULL,
  `Width` decimal(10,2) DEFAULT NULL,
  `YardPaved` bit(1) DEFAULT NULL,
  `amenities_MOD` bit(1) DEFAULT NULL,
  `IsMultiplePolygonsNeeded` bit(1) DEFAULT NULL,
  `IsSkipped` bit(1) DEFAULT NULL,
  `LTSLeaseResearchStatus` enum('Grouping Completed','Grouping InProgress','Not Started','Merge Completed','Linked To Tenant','Merge InProgress','Linked To Tenant InProgress') DEFAULT NULL,
  `NeedsResearchComments` text,
  `RecordedOwners` text,
  `ResearchTypeID` int DEFAULT NULL,
  `ResearchTypeName` varchar(25) DEFAULT NULL,
  `TrueOwners` text,
  `TSATenantResearchStatus` enum('Grouping Completed','Grouping InProgress','Not Started','Merge InProgress','Merge Completed','Stacking InProgress','Stacking Completed') DEFAULT NULL,
  `internalToolFields_MOD` bit(1) DEFAULT NULL,
  `IsActive` bit(1) DEFAULT NULL,
  `isActive_MOD` bit(1) DEFAULT NULL,
  `Address` varchar(100) DEFAULT NULL,
  `CounsilTaxID` varchar(50) DEFAULT NULL,
  `GeoscapePropertyID` varchar(20) DEFAULT NULL,
  `LegalDescription` varchar(2000) DEFAULT NULL,
  `LGA` varchar(100) DEFAULT NULL,
  `MarketId` int DEFAULT NULL,
  `MetroId` int DEFAULT NULL,
  `ParkComplexName` varchar(100) DEFAULT NULL,
  `SurroundingLandUse` int DEFAULT NULL,
  `UseAddressAsPropertyName` bit(1) DEFAULT NULL,
  `ValuerGeneralID` varchar(50) DEFAULT NULL,
  `Zoning` varchar(100) DEFAULT NULL,
  `ZoningClassID` int DEFAULT NULL,
  `ZoningCode` varchar(100) DEFAULT NULL,
  `location_MOD` bit(1) DEFAULT NULL,
  `ParentPropertyID` int DEFAULT NULL,
  `parentPropertyID_MOD` bit(1) DEFAULT NULL,
  `ActualCompletion` datetime(6) DEFAULT NULL,
  `AskingLeaseRatePerYearMax` decimal(14,3) DEFAULT NULL,
  `AskingLeaseRatePerYearMin` decimal(14,3) DEFAULT NULL,
  `BookValue` int DEFAULT NULL,
  `BookValueDate` datetime(6) DEFAULT NULL,
  `BuildingComments` text,
  `BuildingWebsite` varchar(255) DEFAULT NULL,
  `ClassTypeID` int DEFAULT NULL,
  `CondoTypeID` int DEFAULT NULL,
  `ConstructionStartDate` datetime(6) DEFAULT NULL,
  `ConstructionStatusID` int DEFAULT NULL,
  `ConstructionTypeID` int DEFAULT NULL,
  `CraneServed` bit(1) DEFAULT NULL,
  `CurrentTitle` varchar(255) DEFAULT NULL,
  `DeletedAt` datetime(6) DEFAULT NULL,
  `DirectOccupiedPercentage` decimal(5,2) DEFAULT NULL,
  `EarthquakeZoneID` int DEFAULT NULL,
  `EnergyStarRatingID` int DEFAULT NULL,
  `EstCompletion` datetime(6) DEFAULT NULL,
  `EstimatedCompletionDate` date DEFAULT NULL,
  `Floors` int DEFAULT NULL,
  `GovernmentInterestID` int DEFAULT NULL,
  `GreenStarRatingID` int DEFAULT NULL,
  `GRESBScore` decimal(10,2) DEFAULT NULL,
  `GRESBScoreMax` int DEFAULT NULL,
  `GRESBScoreMin` int DEFAULT NULL,
  `GroupID` int DEFAULT NULL,
  `HasSolar` bit(1) DEFAULT NULL,
  `HVACTypeID` int DEFAULT NULL,
  `IsADAAccessible` bit(1) DEFAULT NULL,
  `IsEnergyStar` bit(1) DEFAULT NULL,
  `IsOwnerOccupied` bit(1) DEFAULT NULL,
  `IsVented` bit(1) DEFAULT NULL,
  `LandUse` int DEFAULT NULL,
  `LastReviewedBy` int DEFAULT NULL,
  `LastReviewedDate` datetime(6) DEFAULT NULL,
  `ListingCompany` text,
  `MainPhotoUrl` varchar(255) DEFAULT NULL,
  `MaxAskingSalePrice` decimal(14,3) DEFAULT NULL,
  `MinAskingSalePrice` decimal(14,3) DEFAULT NULL,
  `MixedUseAllocation` varchar(100) DEFAULT NULL,
  `NoOfOfficeFloors` int DEFAULT NULL,
  `OccupiedPercentage` decimal(5,2) DEFAULT NULL,
  `ParcelInfo` text,
  `PropertyKey` varchar(36) DEFAULT NULL,
  `RoofTypeID` int DEFAULT NULL,
  `SpecificUseID` int DEFAULT NULL,
  `SpecificUseName` varchar(50) DEFAULT NULL,
  `SprinklerTypeID` int DEFAULT NULL,
  `TenancyTypeID` int DEFAULT NULL,
  `TIAllowance` decimal(10,2) DEFAULT NULL,
  `TitleReferenceDate` datetime(6) DEFAULT NULL,
  `TrafficCount` int DEFAULT NULL,
  `UseTypeID` int DEFAULT NULL,
  `UseTypeName` varchar(25) DEFAULT NULL,
  `WaterStarRatingID` int DEFAULT NULL,
  `YearBuilt` int DEFAULT NULL,
  `YearRenovated` int DEFAULT NULL,
  `propertyDetails_MOD` bit(1) DEFAULT NULL,
  `PropertyName` varchar(255) DEFAULT NULL,
  `propertyName_MOD` bit(1) DEFAULT NULL,
  `GBASizeSource` varchar(45) DEFAULT NULL,
  `NRASizeSourceID` int DEFAULT NULL,
  `Awnings` bit(1) DEFAULT NULL,
  `AwningsCount` int DEFAULT NULL,
  `AwningsSizeSF` decimal(14,3) DEFAULT NULL,
  `BuildingSize` decimal(12,3) DEFAULT NULL,
  `BuildingSizeSF` decimal(14,3) DEFAULT NULL,
  `ClearHeightMax` decimal(12,3) DEFAULT NULL,
  `ClearHeightMin` decimal(12,3) DEFAULT NULL,
  `ContributedGBA` decimal(12,3) DEFAULT NULL,
  `ContributedGBASizeSF` decimal(14,3) DEFAULT NULL,
  `ContributedGBASizeSourceID` int DEFAULT NULL,
  `GLA` decimal(12,3) DEFAULT NULL,
  `GLASizeSF` decimal(14,3) DEFAULT NULL,
  `GLAR` decimal(12,3) DEFAULT NULL,
  `GLARSizeSF` decimal(14,3) DEFAULT NULL,
  `GLARSizeSourceID` int DEFAULT NULL,
  `GLASizeSourceID` int DEFAULT NULL,
  `HardstandArea` decimal(10,2) DEFAULT NULL,
  `HardstandAreaSourceID` int DEFAULT NULL,
  `LargestFloor` int DEFAULT NULL,
  `LotSize` decimal(12,3) DEFAULT NULL,
  `LotSizeAC` decimal(12,3) DEFAULT NULL,
  `LotSizeSF` decimal(14,3) DEFAULT NULL,
  `LotSizeSourceID` int DEFAULT NULL,
  `MaxFloorSize` decimal(12,3) DEFAULT NULL,
  `Mezzanine` bit(1) DEFAULT NULL,
  `MezzanineSizeSF` decimal(14,3) DEFAULT NULL,
  `MinFloorSize` decimal(12,3) DEFAULT NULL,
  `NLA` decimal(12,3) DEFAULT NULL,
  `NLAAC` decimal(12,3) DEFAULT NULL,
  `OfficeSize` decimal(12,3) DEFAULT NULL,
  `RetailFrontage` decimal(12,3) DEFAULT NULL,
  `RetailSize` decimal(12,3) DEFAULT NULL,
  `SizeSourceID` int DEFAULT NULL,
  `SmallestFloor` int DEFAULT NULL,
  `TotalAvailableSF` decimal(14,3) DEFAULT NULL,
  `TotalSaleSizeSF` decimal(14,3) DEFAULT NULL,
  `TypicalFloorSize` decimal(12,3) DEFAULT NULL,
  `propertySize_MOD` bit(1) DEFAULT NULL,
  `addresses_MOD` bit(1) DEFAULT NULL,
  `genuses_MOD` bit(1) DEFAULT NULL,
  `AddressID` int DEFAULT NULL,
  `LocationID` int DEFAULT NULL,
  `SubMarketID` int DEFAULT NULL,
  PRIMARY KEY (`PropertyID`,`revision_id`),
  CONSTRAINT `FK_PropertyAudit_RevisionInfo` FOREIGN KEY (`revision_id`) REFERENCES `Revision_Info` (`id`)
) ;

CREATE TABLE IF NOT EXISTS `Empirical_Prod`.`PropertyResearchStatus_Audit` (
  `propertyResearchStatusId` int NOT NULL,
  `revision_id` int NOT NULL,
  `revision_type` tinyint DEFAULT NULL,
  `IsActive` bit(1) DEFAULT NULL,
  `isActive_MOD` bit(1) DEFAULT NULL,
  `propertyResearchTypeId` int DEFAULT NULL,
  `propertyResearchTypeId_MOD` bit(1) DEFAULT NULL,
  `propertyID` int DEFAULT NULL,
  `property_MOD` bit(1) DEFAULT NULL,
  PRIMARY KEY (`propertyResearchStatusId`,`revision_id`),
  CONSTRAINT `FK_PropertyResearchStatusAudit_RevisionInfo` FOREIGN KEY (`revision_id`) REFERENCES `Revision_Info` (`id`)
) ;

CREATE TABLE IF NOT EXISTS `Empirical_Prod`.`Use_Audit` (
  `UseID` int NOT NULL,
  `revision_id` int NOT NULL,
  `revision_type` tinyint DEFAULT NULL,
  `AvgFloorSizeSF` decimal(12,4) DEFAULT NULL,
  `avgFloorSizeSF_MOD` bit(1) DEFAULT NULL,
  `Floors` varchar(11) DEFAULT NULL,
  `floors_MOD` bit(1) DEFAULT NULL,
  `IsActive` tinyint(1) DEFAULT NULL,
  `isActive_MOD` bit(1) DEFAULT NULL,
  `Notes` text,
  `notes_MOD` bit(1) DEFAULT NULL,
  `ParentTableID` int DEFAULT NULL,
  `parentTableId_MOD` bit(1) DEFAULT NULL,
  `Sequence` int DEFAULT NULL,
  `sequence_MOD` bit(1) DEFAULT NULL,
  `ParentID` int DEFAULT NULL,
  `property_MOD` bit(1) DEFAULT NULL,
  `SpecificUsesID` int DEFAULT NULL,
  `UseTypeID` int DEFAULT NULL,
  PRIMARY KEY (`revision_id`,`UseID`),
  CONSTRAINT `FK_UseAudit_RevisionInfo` FOREIGN KEY (`revision_id`) REFERENCES `Revision_Info` (`id`)
) ;
