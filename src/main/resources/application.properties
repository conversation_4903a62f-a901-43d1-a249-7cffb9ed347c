spring.application.name=apicore

# Database Configuration
spring.datasource.empirical-prod.jdbc-url=*******************************************************************************
spring.datasource.empirical-prod.username=dbuser-arealytics-app
spring.datasource.empirical-prod.password=DBUserArealyticsAapp#622
spring.datasource.empirical-prod.driver-class-name=com.mysql.cj.jdbc.Driver

spring.datasource.empirical-gis.jdbc-url=******************************************************************************
spring.datasource.empirical-gis.username=dbuser-arealytics-app
spring.datasource.empirical-gis.password=DBUserArealyticsAapp#622
spring.datasource.empirical-gis.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA/Hibernate Configuration
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.hibernate.ddl-auto=validate

 # Hibernate Envers Configuration
spring.jpa.properties.org.hibernate.envers.audit_table_suffix=_Audit
spring.jpa.properties.org.hibernate.envers.revision_field_name=revision_id
spring.jpa.properties.org.hibernate.envers.revision_type_field_name=revision_type
# stores the data at delete
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true

#spring.mvc.servlet.path=/api
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jackson.deserialization.fail-on-unknown-properties=true

flyway.transaction.location=classpath:db/migration/TransactionSchema
flyway.gis.location=classpath:db/migration/GISSchema

# Logbook configuration
logbook.format.style=http
logbook.include[0]=request
logbook.custom.response-logging-enabled=true
logbook.obfuscate.headers[0]=Authorization
logbook.filter.enabled=true

# Logging levels
logging.level.org.zalando.logbook=TRACE
logging.level.org.zalando.logbook.Logbook=TRACE
logging.level.org.zalando.logbook.HttpLogWriter=TRACE
logging.level.org.zalando.logbook.LogbookAutoConfiguration=TRACE

# Logging pattern
logging.pattern.console=%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

#CORS Configration
cors.allowed-origins=http://localhost:4200,http://localhost:4400,http://localhost:4500,http://localhost:8080,https://*.arealytics.com.au


server.port:8081

#swagger details
swagger.server.url=http://localhost:8081
swagger.server.description=Local Dev Server
