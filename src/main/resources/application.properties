spring.application.name=apicore

# Database Configuration
spring.datasource.empirical-prod.jdbc-url=*******************************************************************************
spring.datasource.empirical-prod.username=dbuser-arealytics-app
spring.datasource.empirical-prod.password=DBUserArealyticsAapp#622
spring.datasource.empirical-prod.driver-class-name=com.mysql.cj.jdbc.Driver

spring.datasource.empirical-gis.jdbc-url=******************************************************************************
spring.datasource.empirical-gis.username=dbuser-arealytics-app
spring.datasource.empirical-gis.password=DBUserArealyticsAapp#622
spring.datasource.empirical-gis.driver-class-name=com.mysql.cj.jdbc.Driver

# S3 bucket Configuration
aws.s3.bucket.name=empiricalcres3
aws.s3.bucket.region=us-east-1
aws.s3.access-key-id=********************
aws.s3.secret-access-key=jaB1KhbI5MWVGYAiZVvTjvSPE2m+EcD9s8jksGUJ
aws.s3.acl=public-read
aws.s3.path=EmpiricalCRE/PHOENIX
aws.s3.upload-max-size=50mb
aws.s3.base=https://media.empiricalcre.com
aws.s3.image.market-brief=/MarketBrief/Media/

# AWS logging
logging.level.software.amazon.awssdk=DEBUG
logging.level.software.amazon.awssdk.request=TRACE

# JPA/Hibernate Configuration
# spring.jpa.show-sql=true
# spring.jpa.properties.hibernate.format_sql=true
# spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.hibernate.ddl-auto=validate

 # Hibernate Envers Configuration
spring.jpa.properties.org.hibernate.envers.audit_table_suffix=_Audit
spring.jpa.properties.org.hibernate.envers.revision_field_name=revision_id
spring.jpa.properties.org.hibernate.envers.revision_type_field_name=revision_type
# stores the data at delete
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true


# application.properties
# spring.jpa.show-sql=true
# spring.jpa.properties.hibernate.format_sql=true
# logging.level.org.hibernate.SQL=DEBUG
# logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
x
#spring.mvc.servlet.path=/api
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jackson.deserialization.fail-on-unknown-properties=true

flyway.transaction.location=classpath:db/migration/TransactionSchema
flyway.gis.location=classpath:db/migration/GISSchema
flyway.baseline=2

# Content negotiation
spring.mvc.contentnegotiation.favor-path-extension=false
spring.mvc.contentnegotiation.favor-parameter=false
spring.mvc.contentnegotiation.media-types.json=application/json
spring.mvc.default-content-type=application/json

# Springdoc OpenAPI config
springdoc.default-produces-media-type=application/json
springdoc.default-consumes-media-type=application/json

# Logbook configuration
# logbook.format.style=http
# logbook.include[0]=request
# logbook.custom.response-logging-enabled=true
# logbook.obfuscate.headers[0]=Authorization
# logbook.filter.enabled=true

# Logging levels
# logging.level.org.zalando.logbook=TRACE
# logging.level.org.zalando.logbook.Logbook=TRACE
# logging.level.org.zalando.logbook.HttpLogWriter=TRACE
# logging.level.org.zalando.logbook.LogbookAutoConfiguration=TRACE

# Enable detailed error logging
logging.level.com.arealytics.core.exception=DEBUG
logging.level.org.springframework.web=DEBUG
server.error.include-message=always
server.error.include-binding-errors=always
server.error.include-stacktrace=on_param
server.error.include-exception=true

# Logging pattern
# logging.pattern.console=%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

logging.level.net.ttddyy.dsproxy.listener.logging.SLF4JQueryLoggingListener=DEBUG

# Logging for Redis
logging.level.org.springframework.data.redis=DEBUG
logging.level.io.lettuce.core=DEBUG

#CORS Configration
cors.allowed-origins=http://localhost:4200,http://localhost:4400,http://localhost:4500,http://localhost:8080,https://*.arealytics.com.au


server.port:8081

# Redis Configuration
redis.host=localhost
redis.port=6379
spring.data.redis.repositories.enabled=false

#swagger details
swagger.server.url=http://localhost:8081
swagger.server.description=Local Dev Server
