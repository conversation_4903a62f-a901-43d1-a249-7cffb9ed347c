package com.arealytics.core.querydsl;

import com.arealytics.core.domain.empiricalProd.QAddress;
import com.arealytics.core.domain.empiricalProd.QProperty;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class QClassTest {

    @Test
    public void testQAddressExists() {
        QAddress qAddress = QAddress.address;
        assertNotNull(qAddress);
        assertNotNull(qAddress.addressId);
        assertNotNull(qAddress.isActive);
    }

    @Test
    public void testQPropertyExists() {
        QProperty qProperty = QProperty.property;
        assertNotNull(qProperty);
        assertNotNull(qProperty.propertyID);
    }
}
