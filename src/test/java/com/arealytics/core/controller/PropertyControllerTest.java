package com.arealytics.core.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.arealytics.core.dto.request.AdditionalAddressRequestDTO;
import com.arealytics.core.dto.request.ParcelPropertyRequestDTO;
import com.arealytics.core.dto.response.AdditionalAddressDTO;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.dto.response.ParcelPropertyDTO;
import com.arealytics.core.dto.response.PropertyStrataDetailsDTO;
import com.arealytics.core.service.AddressService;
import com.arealytics.core.service.ParcelService;
import com.arealytics.core.service.PropertyStrataService;

import jakarta.validation.constraints.Null;

@ExtendWith(MockitoExtension.class)
class PropertyControllerTest {
  
  @Mock
  private ParcelService parcelService;

  @Mock
  private AddressService addressService;
  
  @Mock
  private PropertyStrataService propertyStrataService;
  private AdditionalAddressRequestDTO additionalAddressRequestDTO;
  private AdditionalAddressDTO additionalAddressDTO;

  @InjectMocks
  private PropertyController propertyController;

  private ParcelPropertyRequestDTO parcelPropertyRequestDTO;

  private ParcelPropertyDTO parcelPropertyDTO;

  private PropertyStrataDetailsDTO propertyStrataDetailsDTO;


  

  @BeforeEach
  void setUp() {
    parcelPropertyRequestDTO = new ParcelPropertyRequestDTO();
    parcelPropertyRequestDTO.setBlock("34");
    parcelPropertyRequestDTO.setLot("3");
    parcelPropertyRequestDTO.setParcelNo("2//DP3445");
    parcelPropertyRequestDTO.setParcelSF(345.66);
    parcelPropertyRequestDTO.setSubDivision("subdivision");

    additionalAddressRequestDTO = new AdditionalAddressRequestDTO();
    additionalAddressRequestDTO.setIsActive(true);
    additionalAddressRequestDTO.setAddressStreetName("Main");
    additionalAddressRequestDTO.setCityId(1295);
    additionalAddressRequestDTO.setZipCode(2028);
    additionalAddressRequestDTO.setParentId(113364);
    additionalAddressRequestDTO.setBuildingNumber("5-6/98");
  }

  @Test
  void getParcelByPropertyId_ShouldReturnParcelList() {
    // Arrange
    List<ParcelPropertyDTO> parcelList = Arrays.asList(parcelPropertyDTO);
    when(parcelService.getParcelByProperty(123)).thenReturn(parcelList);

    // Act
    ResponseEntity<ApiResponse<List<ParcelPropertyDTO>>> response = propertyController.getParcelByProperty(123);

    // Assert
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("Success", response.getBody().getMessage());
    verify(parcelService, times(1)).getParcelByProperty(123);
  }

  @Test
  void createParcelProperty_ShouldReturnCreatedParcel() {
    // Arrange
    when(parcelService.createParcel(parcelPropertyRequestDTO, 123)).thenReturn(parcelPropertyDTO);

    // Act
    ResponseEntity<ApiResponse<ParcelPropertyDTO>> response = propertyController.createParcel(parcelPropertyRequestDTO, 123);

    // Assert
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("Success", response.getBody().getMessage());
    verify(parcelService, times(1)).createParcel(parcelPropertyRequestDTO, 123);
  }

  @Test
  void updateParcelProperty_ShouldReturnUpdatedParcel() {
    ParcelPropertyRequestDTO requestDTO = new ParcelPropertyRequestDTO();
    requestDTO.setBlock("34");
    requestDTO.setLot("3");

    // Arrange
    when(parcelService.updateParcel(requestDTO, 344, 123)).thenReturn(parcelPropertyDTO);

    // Act
    ResponseEntity<ApiResponse<ParcelPropertyDTO>> response = propertyController.updateParcel(requestDTO, 123, 344);

    // Assert
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("Success", response.getBody().getMessage());
    verify(parcelService, times(1)).updateParcel(requestDTO, 344, 123);
  }
  
  @Test
  void deleteParcelProperty_ShouldReturnSuccess() {
    // Arrange
    doNothing().when(parcelService).deleteParcel(344, 123);

    // Act
    ResponseEntity<ApiResponse<Null>> response = propertyController.deleteParcel(123, 344);

    // Assert
    assertNotNull(response);
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertEquals("Success", response.getBody().getMessage());
    assertEquals(HttpStatus.OK.value(), response.getBody().getStatus());
    assertNull(response.getBody().getResponseData());

    // Verify the service was called once
    verify(parcelService, times(1)).deleteParcel(344, 123);
  }

  @Test
  void getPropertyStrataDetails_ShouldReturnStrataDetails() {
    // Arrange
    List<PropertyStrataDetailsDTO> propertyList = Arrays.asList(propertyStrataDetailsDTO);
    when(propertyStrataService.getLinkedPropertyDetails(123)).thenReturn(propertyList);

    // Act
    ResponseEntity<ApiResponse<List<PropertyStrataDetailsDTO>>> response = propertyController.getLinkedPropertyDetails(123);

    // Assert
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("Success", response.getBody().getMessage());
    verify(propertyStrataService, times(1)).getLinkedPropertyDetails(123);
  }

  @Test
  void getAdditionalAddress_ShouldReturnAddressList() {
    // Arrange
    Integer propertyId = 113364;
    List<AdditionalAddressDTO> addressList = Arrays.asList(additionalAddressDTO);
    when(addressService.getAdditionalAddress(propertyId)).thenReturn(addressList);

    // Act
    ResponseEntity<ApiResponse<List<AdditionalAddressDTO>>> response = propertyController
        .getAdditionalAddress(propertyId);

    // Assert
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("Success", response.getBody().getMessage());
    verify(addressService, times(1)).getAdditionalAddress(propertyId);
  }

  @Test
  void saveAdditionalAddress_ShouldReturnAddressList() {
    // Arrange
    AdditionalAddressDTO additionalAddressList = additionalAddressDTO;
    when(addressService.saveAdditionalAddress(additionalAddressRequestDTO)).thenReturn(additionalAddressList);

    // Act
    ResponseEntity<ApiResponse<AdditionalAddressDTO>> response = propertyController
        .saveAdditionalAddress(additionalAddressRequestDTO);

    // Assert
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("Success", response.getBody().getMessage());
    verify(addressService, times(1)).saveAdditionalAddress(additionalAddressRequestDTO);
  }

  @Test
  void updateAdditionalAddress_ShouldReturnAdditionalAddress() {
    Integer additionalAddressId = 2051899;
    AdditionalAddressRequestDTO requestDTO = new AdditionalAddressRequestDTO();
    requestDTO.setAddressStreetName("Main Block");
    requestDTO.setBuildingNumber("5-6-98/1");

    // Arrange
    when(addressService.updateAdditionalAddress(additionalAddressId, requestDTO)).thenReturn(additionalAddressDTO);

    // Act
    ResponseEntity<ApiResponse<AdditionalAddressDTO>> response = propertyController.updateAdditionalAddress(additionalAddressId ,requestDTO);

    // Assert
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("Success", response.getBody().getMessage());
    verify(addressService, times(1)).updateAdditionalAddress(additionalAddressId ,requestDTO);
  }

  @Test
  void DeleteAdditionalAddress_ShouldReturnSuccess() {
      // Arrange
      Integer additionalAddressId = 2051899;
      doNothing().when(addressService).deleteAdditionalAddress(additionalAddressId);

      // Act
      ResponseEntity<ApiResponse<Null>> response = propertyController.deleteAdditionalAddress(additionalAddressId);

      // Assert
      assertEquals(HttpStatus.OK, response.getStatusCode());
      assertNotNull(response.getBody());
      assertEquals("Success", response.getBody().getMessage());
      verify(addressService, times(1)).deleteAdditionalAddress(additionalAddressId);
  }

}
