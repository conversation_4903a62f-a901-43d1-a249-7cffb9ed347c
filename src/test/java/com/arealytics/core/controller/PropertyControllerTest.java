package com.arealytics.core.controller;

import com.arealytics.core.dto.request.PropertyDTO;
import com.arealytics.core.dto.response.ApiResponse;
import com.arealytics.core.dto.response.PropertyResponseDTO;
import com.arealytics.core.enumeration.StatusCode;
import com.arealytics.core.exception.ResourceNotFoundException;
import com.arealytics.core.service.BuildingFootPrintService;
import com.arealytics.core.service.ParcelService;
import com.arealytics.core.service.PropertyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PropertyControllerTest {

    @Mock
    private PropertyService propertyService;

    @Mock
    private ParcelService parcelService;

    @Mock
    private BuildingFootPrintService buildingFootPrintService;

    @InjectMocks
    private PropertyController propertyController;

    private PropertyDTO validPropertyDTO;
    private PropertyResponseDTO propertyResponseDTO;

    @BeforeEach
    void setUp() {
        // Initialize valid PropertyDTO for reuse
        validPropertyDTO = new PropertyDTO();
        validPropertyDTO.setPropertyName("Downtown Office");
        validPropertyDTO.setAddress("123 Main St");
        validPropertyDTO.setLatitude(new BigDecimal("40.7128"));
        validPropertyDTO.setLongitude(new BigDecimal("-74.0060"));
        validPropertyDTO.setIsActive(true);
        validPropertyDTO.setYearBuilt(2000);
        validPropertyDTO.setFloors(10);
        validPropertyDTO.setBuildingSizeSF(new BigDecimal("100000"));

        // Initialize PropertyResponseDTO for reuse
        propertyResponseDTO = new PropertyResponseDTO();
        propertyResponseDTO.setPropertyID(1);
    }

    @Test
    void testCreateProperty_Success() {
        // Arrange
        when(propertyService.createProperty(any(PropertyDTO.class))).thenReturn(propertyResponseDTO);

        // Act
        ResponseEntity<ApiResponse<PropertyResponseDTO>> response =
                propertyController.createProperty(validPropertyDTO);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        ApiResponse<PropertyResponseDTO> apiResponse = response.getBody();
        assertNotNull(apiResponse);
        assertFalse(apiResponse.isError());
        assertEquals("Success", apiResponse.getMessage());
        assertEquals(propertyResponseDTO, apiResponse.getResponseData());
        assertEquals(StatusCode.SUCCESS.getStatus().value(), apiResponse.getStatus());
        assertEquals(1, apiResponse.getResponseData().getPropertyID());
    }

    @Test
    void testCreateProperty_InvalidInput() {
        // Arrange
        PropertyDTO invalidPropertyDTO = new PropertyDTO(); // Missing required fields
        when(propertyService.createProperty(any(PropertyDTO.class)))
                .thenThrow(new IllegalArgumentException("Property name and address are required"));

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                propertyController.createProperty(invalidPropertyDTO));
        assertEquals("Property name and address are required", exception.getMessage());
    }

    @Test
    void testCreateProperty_ServiceError() {
        // Arrange
        when(propertyService.createProperty(any(PropertyDTO.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                propertyController.createProperty(validPropertyDTO));
        assertEquals("Database connection failed", exception.getMessage());
    }

    @Test
    void testUpdateProperty_Success() {
        // Arrange
        validPropertyDTO.setPropertyID(1);
        when(propertyService.updateProperty(any(PropertyDTO.class))).thenReturn(propertyResponseDTO);

        // Act
        ResponseEntity<ApiResponse<PropertyResponseDTO>> response =
                propertyController.updateProperty(validPropertyDTO);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        ApiResponse<PropertyResponseDTO> apiResponse = response.getBody();
        assertNotNull(apiResponse);
        assertFalse(apiResponse.isError());
        assertEquals("Success", apiResponse.getMessage());
        assertEquals(propertyResponseDTO, apiResponse.getResponseData());
        assertEquals(StatusCode.SUCCESS.getStatus().value(), apiResponse.getStatus());
        assertEquals(1, apiResponse.getResponseData().getPropertyID());
    }

    @Test
    void testUpdateProperty_NotFound() {
        // Arrange
        validPropertyDTO.setPropertyID(1);
        when(propertyService.updateProperty(any(PropertyDTO.class)))
                .thenThrow(new ResourceNotFoundException("Property not found with ID: 1"));

        // Act & Assert
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () ->
                propertyController.updateProperty(validPropertyDTO));
        assertEquals("Property not found with ID: 1", exception.getMessage());
    }

    @Test
    void testUpdateProperty_InvalidInput_MissingPropertyID() {
        // Arrange
        PropertyDTO invalidPropertyDTO = new PropertyDTO();
        invalidPropertyDTO.setPropertyName("Updated Downtown Office");
        invalidPropertyDTO.setAddress("123 Main St");
        // propertyID is null

        when(propertyService.updateProperty(any(PropertyDTO.class)))
                .thenThrow(new IllegalArgumentException("Property ID is required"));

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                propertyController.updateProperty(invalidPropertyDTO));
        assertEquals("Property ID is required", exception.getMessage());
    }
}
