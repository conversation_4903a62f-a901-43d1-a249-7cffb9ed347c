package com.arealytics.core.service;

import com.arealytics.core.domain.empiricalProd.*;
import com.arealytics.core.dto.request.PropertyDTO;
import com.arealytics.core.dto.response.PropertyDetailsResponseDTO;
import com.arealytics.core.dto.response.PropertyResponseDTO;
import com.arealytics.core.mapper.PropertyMapper;
import com.arealytics.core.repository.empiricalProd.PropertyRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class PropertyServiceTest {

    @InjectMocks
    private PropertyService propertyService;

    @Mock
    private PropertyRepository propertyRepository;

    @Mock
    private LocationService locationService;

    @Mock
    private AddressService addressService;

    @Mock
    private PropertyResearchStatusService propertyResearchStatusService;

    @Mock
    private PropertyUseService propertyUseService;

    @Mock
    private MasterPropertyService masterPropertyService;

    @Mock
    private PropertyAmenitiesService propertyAmenitiesService; // Added mock

    @Mock
    private PropertyMapper propertyMapper;

    private PropertyDTO propertyDTO;
    private Property property;
    private PropertyResponseDTO propertyResponseDTO;
    private Location location;
    private Address address;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        propertyDTO = new PropertyDTO();
        propertyDTO.setPropertyID(1);
        propertyDTO.setEntityId(1001);
        propertyDTO.setAmenities("BATHROOM_AMENITIES,CHILDCARE_CENTER,END_OF_TRIP"); // Set enum names
        propertyDTO.setLatitude(BigDecimal.valueOf(10.34));
        propertyDTO.setLongitude(BigDecimal.valueOf(20.0));

        property = new Property();
        property.setPropertyID(1);
        property.setLocation(new PropertyLocation());

        propertyResponseDTO = new PropertyResponseDTO();

        location = new Location();
        location.setLocationID(100);

        address = new Address();
    }

    @Test
    void testCreateProperty_success() {
        // Given
        when(propertyMapper.toEntity(eq(propertyDTO), any(Property.class))).thenReturn(property);
        when(locationService.createLocation(propertyDTO)).thenReturn(location);
        when(propertyRepository.save(any(Property.class))).thenReturn(property);
        when(addressService.createAddress(propertyDTO, 100, 1)).thenReturn(address);
        when(propertyMapper.toDto(property)).thenReturn(propertyResponseDTO);

        // When
        PropertyResponseDTO response = propertyService.createProperty(propertyDTO);

        // Then
        assertNotNull(response);
        verify(propertyUseService).setUseDetailsForCreate(propertyDTO, property);
        verify(propertyUseService).saveUseRecord(propertyDTO, property);
        verify(propertyResearchStatusService).createDefaultResearchStatus(property);
        verify(propertyAmenitiesService).savePropertyAmenities(
                property.getPropertyID(),
                "BATHROOM_AMENITIES,CHILDCARE_CENTER,END_OF_TRIP",
                1001
        );
    }

    @Test
    void testCreateProperty_emptyAmenities() {
        // Given
        propertyDTO.setAmenities(""); // Empty amenities
        when(propertyMapper.toEntity(eq(propertyDTO), any(Property.class))).thenReturn(property);
        when(locationService.createLocation(propertyDTO)).thenReturn(location);
        when(propertyRepository.save(any(Property.class))).thenReturn(property);
        when(addressService.createAddress(propertyDTO, 100, 1)).thenReturn(address);
        when(propertyMapper.toDto(property)).thenReturn(propertyResponseDTO);

        // When
        PropertyResponseDTO response = propertyService.createProperty(propertyDTO);

        // Then
        assertNotNull(response);
        verify(propertyAmenitiesService).savePropertyAmenities(property.getPropertyID(), "", 1001);
    }

    @Test
    void testUpdateProperty_success() {
        // Given
        Property existingProperty = new Property();
        existingProperty.setPropertyID(1);
        existingProperty.setLocation(new PropertyLocation());
        existingProperty.getLocation().setLocation(location);
        existingProperty.getLocation().setAddressId(address);

        propertyDTO.setAmenities("CHILDCARE_CENTER,END_OF_TRIP"); // Updated amenities

        when(propertyRepository.findById(1)).thenReturn(Optional.of(existingProperty));
        when(propertyMapper.toEntity(propertyDTO, existingProperty)).thenReturn(property);
        when(locationService.updateLocation(propertyDTO, location.getLocationID())).thenReturn(location);
        when(addressService.updateAddress(propertyDTO, location.getLocationID(), 1)).thenReturn(address);
        when(propertyRepository.save(property)).thenReturn(property);
        when(propertyMapper.toDto(property)).thenReturn(propertyResponseDTO);

        // When
        PropertyResponseDTO response = propertyService.updateProperty(propertyDTO);

        // Then
        assertNotNull(response);
        verify(propertyUseService).setUseDetailsForUpdate(propertyDTO, existingProperty, property);
        verify(propertyUseService).saveUseRecord(propertyDTO, property);
        verify(masterPropertyService).processMasterStrataRelationships(property);
        verify(propertyAmenitiesService).savePropertyAmenities(
                property.getPropertyID(),
                "CHILDCARE_CENTER,END_OF_TRIP",
                1001
        );
    }

    @Test
    void testUpdateProperty_nullAmenities() {
        // Given
        Property existingProperty = new Property();
        existingProperty.setPropertyID(1);
        existingProperty.setLocation(new PropertyLocation());
        existingProperty.getLocation().setLocation(location);
        existingProperty.getLocation().setAddressId(address);

        propertyDTO.setAmenities(null); // Null amenities

        when(propertyRepository.findById(1)).thenReturn(Optional.of(existingProperty));
        when(propertyMapper.toEntity(propertyDTO, existingProperty)).thenReturn(property);
        when(locationService.updateLocation(propertyDTO, location.getLocationID())).thenReturn(location);
        when(addressService.updateAddress(propertyDTO, location.getLocationID(), 1)).thenReturn(address);
        when(propertyRepository.save(property)).thenReturn(property);
        when(propertyMapper.toDto(property)).thenReturn(propertyResponseDTO);

        // When
        PropertyResponseDTO response = propertyService.updateProperty(propertyDTO);

        // Then
        assertNotNull(response);
        verify(propertyAmenitiesService).savePropertyAmenities(property.getPropertyID(), null, 1001);
    }

    @Test
    void testUpdateProperty_propertyNotFound() {
        // Given
        when(propertyRepository.findById(1)).thenReturn(Optional.empty());
        propertyDTO.setPropertyID(1);

        // Then
        RuntimeException ex = assertThrows(RuntimeException.class, () ->
                propertyService.updateProperty(propertyDTO));
        assertTrue(ex.getMessage().contains("Property not found"));
        verify(propertyAmenitiesService, never()).savePropertyAmenities(anyInt(), anyString(), anyInt());
    }

    @Test
    void testUpdateProperty_nullId() {
        // Given
        propertyDTO.setPropertyID(null);

        // Then
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, () ->
                propertyService.updateProperty(propertyDTO));
        assertEquals("Property ID must not be null for update", ex.getMessage());
        verify(propertyAmenitiesService, never()).savePropertyAmenities(anyInt(), anyString(), anyInt());
    }
}
