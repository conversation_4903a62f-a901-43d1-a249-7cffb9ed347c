services:
  phoenix-app:
    container_name: phoenix-app
    build:
      context: .
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8080}:8080"
    environment:
      - JAVA_OPTS=${JAVA_OPTS}
      - SPRING_DATASOURCE_URL=**********************************************************************************/${DB_DATABASE_NAME}?zeroDateTimeBehavior=CONVERT_TO_NULL
      - SPRING_DATASOURCE_USERNAME=${DB_DEV_USERNAME}
      - SPRING_DATASOURCE_PASSWORD=${DB_DEV_PASSWORD}
      - SERVER_PORT=8080
      - SPRING_DATASOURCE_DRIVER_CLASS_NAME=com.mysql.cj.jdbc.Driver
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
    volumes:
      - app-logs:/app/logs
      - app-uploads:/app/uploads
      - heap-dumps:/tmp/heap-dumps
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
networks:
  app-network:
    driver: bridge
volumes:
  app-logs:
    driver: local
  app-uploads:
    driver: local
  heap-dumps:
    driver: local
