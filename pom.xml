<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.5</version>
		<relativePath/>
	</parent>

	<groupId>com.arealytics</groupId>
	<artifactId>core</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>core</name>
	<description>API CORE</description>

	<properties>
		<java.version>21</java.version>
		<spotless.version>2.44.3</spotless.version>
		<querydsl.version>5.0.0</querydsl.version>
    <net.ttddyy.version>1.7</net.ttddyy.version>
	</properties>

	<dependencies>
		<!-- Spring Boot Dependencies -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-logging</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- Database and ORM -->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-envers</artifactId>
			<version>6.4.2.Final</version>
		</dependency>
        <dependency>
           <groupId>org.javers</groupId>
           <artifactId>javers-core</artifactId>
           <version>7.8.0</version>
        </dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-spatial</artifactId>
			<version>6.4.2.Final</version>
		</dependency>
 
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<dependency>
			<groupId>io.lettuce</groupId>
			<artifactId>lettuce-core</artifactId>
			<version>6.6.0.RELEASE</version>
		</dependency>

		<!-- MapStruct -->
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>1.6.3</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
			<version>1.6.3</version>
		</dependency>

		<!-- Lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- Swagger/OpenAPI -->
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.8.6</version>
		</dependency>
		<dependency>
			<groupId>io.swagger.core.v3</groupId>
			<artifactId>swagger-annotations</artifactId>
			<version>2.2.28</version>
		</dependency>

		<!-- Logging -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.zalando</groupId>
			<artifactId>logbook-spring-boot-starter</artifactId>
			<version>3.11.0</version>
		</dependency>

		<!-- Flyway -->
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-core</artifactId>
			<version>11.3.0</version>
		</dependency>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-mysql</artifactId>
			<version>11.3.0</version>
		</dependency>

		<!-- Annotations -->
		<dependency>
			<groupId>javax.annotation</groupId>
			<artifactId>javax.annotation-api</artifactId>
			<version>1.3.2</version>
		</dependency>

		<!-- QueryDSL -->
		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-jpa</artifactId>
			<classifier>jakarta</classifier>
			<version>${querydsl.version}</version>
		</dependency>
		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-apt</artifactId>
			<classifier>jakarta</classifier>
			<version>${querydsl.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-core</artifactId>
			<version>${querydsl.version}</version>
		</dependency>

		<!-- QueryDSL Spatial -->
		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-spatial</artifactId>
			<version>${querydsl.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.vividsolutions</groupId>
					<artifactId>jts</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- JTS Geometry support -->
		<dependency>
			<groupId>org.locationtech.jts</groupId>
			<artifactId>jts-core</artifactId>
			<version>1.19.0</version>
		</dependency>

		<!-- Testing -->
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<scope>test</scope>
		</dependency>

    <!-- Logging sql queries -->
    <dependency>
      <groupId>net.ttddyy</groupId>
      <artifactId>datasource-proxy</artifactId>
      <version>${net.ttddyy.version}</version>
    </dependency>

    <!-- s3 bucket -->
    <dependency>
      <groupId>software.amazon.awssdk</groupId>
      <artifactId>s3</artifactId>
      <version>2.25.26</version>
    </dependency>
	</dependencies>

	<build>
		<plugins>
			<!-- Compiler Plugin -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>1.18.36</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok-mapstruct-binding</artifactId>
							<version>0.2.0</version>
						</path>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>1.6.3</version>
						</path>
						<path>
							<groupId>com.querydsl</groupId>
							<artifactId>querydsl-apt</artifactId>
							<classifier>jakarta</classifier>
							<version>${querydsl.version}</version>
						</path>
						<path>
							<groupId>com.querydsl</groupId>
							<artifactId>querydsl-jpa</artifactId>
							<classifier>jakarta</classifier>
							<version>${querydsl.version}</version>
						</path>
						<path>
							<groupId>jakarta.persistence</groupId>
							<artifactId>jakarta.persistence-api</artifactId>
							<version>3.1.0</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>

			<!-- Spring Boot Plugin -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>

			<!-- QueryDSL APT Plugin -->
			<plugin>
				<groupId>com.mysema.maven</groupId>
				<artifactId>apt-maven-plugin</artifactId>
				<version>1.1.3</version>
				<executions>
					<execution>
						<goals>
							<goal>process</goal>
						</goals>
						<configuration>
							<outputDirectory>target/generated-sources/java</outputDirectory>
							<processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
							<options>
								<querydsl.packageSuffix>.querydsl</querydsl.packageSuffix>
								<querydsl.includedPackages>org.hibernate.envers</querydsl.includedPackages>
								<querydsl.excludedClasses>javax.persistence.EntityManagerFactory</querydsl.excludedClasses>
							</options>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- Spotless Plugin -->
			<!--			<plugin>-->
<!--				<groupId>com.diffplug.spotless</groupId>-->
<!--				<artifactId>spotless-maven-plugin</artifactId>-->
<!--				<version>${spotless.version}</version>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<goals>-->
<!--							<goal>check</goal>-->
<!--							<goal>apply</goal>-->
<!--						</goals>-->
<!--					</execution>-->
<!--				</executions>-->
<!--				<configuration>-->
<!--					<formats>-->
<!--						<format>-->
<!--							<includes>-->
<!--								<include>src/**/*.java</include>-->
<!--							</includes>-->
<!--							<palantirJavaFormat>-->
<!--								<version>2.44.0</version> &lt;!&ndash; Use the latest version available &ndash;&gt;-->
<!--							</palantirJavaFormat>-->
<!--						</format>-->
<!--					</formats>-->
<!--				</configuration>-->
<!--			</plugin>-->
<!--			<plugin>-->
<!--				<groupId>com.diffplug.spotless</groupId>-->
<!--				<artifactId>spotless-maven-plugin</artifactId>-->
<!--				<version>${spotless.version}</version>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<id>spotless-apply</id>-->
<!--						<phase>process-sources</phase>-->
<!--						<goals>-->
<!--							<goal>apply</goal>-->
<!--						</goals>-->
<!--					</execution>-->
<!--				</executions>-->
<!--				<configuration>-->
<!--					<ratchetFrom>develop</ratchetFrom>-->
<!--					<formats>-->
<!--						<format>-->
<!--							<includes>-->
<!--								<include>.gitattributes</include>-->
<!--								<include>.gitignore</include>-->
<!--							</includes>-->
<!--							<trimTrailingWhitespace/>-->
<!--							<endWithNewline/>-->
<!--							<indent>-->
<!--								<tabs>true</tabs>-->
<!--								<spacesPerTab>4</spacesPerTab>-->
<!--							</indent>-->
<!--						</format>-->
<!--					</formats>-->
<!--				</configuration>-->
<!--			</plugin>-->
		</plugins>
	</build>
</project>
