# Arealytics Core API

A Spring Boot application for property analytics and management.

## 🚀 Quick Start

### Prerequisites
- Java 17+
- Maven 3.6+ (or use the included Maven wrapper)
- Git

### Setup Development Environment

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ar-poc-core-api
   ```

2. **Run the setup script**
   ```bash
   ./scripts/setup-dev-environment.sh
   ```

3. **Build the project**
   ```bash
   mvn clean install
   ```

4. **Start the application**
   ```bash
   mvn spring-boot:run
   ```

The application will start on `http://localhost:8080`

## 🔧 Development

### Git Hooks

This project includes automated Git hooks to ensure code quality:

- **Pre-commit hook**: Runs compilation checks and tests before each commit
- **Commit message hook**: Validates commit message format

#### Managing Git Hooks

```bash
# Check hook status
./scripts/manage-git-hooks.sh status

# Test hooks without committing
./scripts/manage-git-hooks.sh test

# Install/reinstall hooks
./scripts/manage-git-hooks.sh install
```

For detailed information, see [Git Hooks Documentation](docs/git-hooks.md).

### Building the Project

```bash
# Clean build
mvn clean install

# Skip tests (not recommended)
mvn clean install -DskipTests

# Generate QueryDSL Q-classes
mvn clean generate-sources

# Run only tests
mvn test
```

### Running the Application

```bash
# Using Maven
mvn spring-boot:run

# Using Maven wrapper
./mvnw spring-boot:run

# With specific profile
mvn spring-boot:run -Dspring.profiles.active=dev
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=PropertyServiceTest

# Run tests with coverage
mvn test jacoco:report
```

### Test Categories

- **Unit Tests**: Fast tests that don't require external dependencies
- **Integration Tests**: Tests that require database or external services
- **API Tests**: End-to-end API testing

## 📁 Project Structure

```
src/
├── main/
│   ├── java/
│   │   └── com/arealytics/core/
│   │       ├── controller/     # REST controllers
│   │       ├── service/        # Business logic
│   │       ├── repository/     # Data access layer
│   │       ├── domain/         # Entity classes
│   │       ├── dto/           # Data transfer objects
│   │       ├── config/        # Configuration classes
│   │       └── exception/     # Exception handling
│   └── resources/
│       ├── application.properties
│       └── static/
└── test/
    └── java/                  # Test classes
```

## 🔍 Code Quality

### Pre-commit Checks

Every commit automatically runs:
- ✅ Compilation check
- ✅ Unit tests
- ✅ QueryDSL Q-class generation
- ✅ Code style validation
- ✅ Common issue detection

### Manual Quality Checks

```bash
# Run checkstyle (if configured)
mvn checkstyle:check

# Run SpotBugs (if configured)
mvn spotbugs:check

# Generate test coverage report
mvn jacoco:report
```

## 🐛 Troubleshooting

### Common Issues

#### QueryDSL Q-classes not found
```bash
mvn clean generate-sources compile
```

#### Compilation errors
```bash
# Check detailed compilation output
mvn clean compile

# Clean everything and rebuild
mvn clean install
```

#### Tests failing
```bash
# Run tests with detailed output
mvn test -X

# Run specific failing test
mvn test -Dtest=FailingTestClass -X
```

#### Git hooks not working
```bash
# Check hook status
./scripts/manage-git-hooks.sh status

# Reinstall hooks
./scripts/manage-git-hooks.sh install
```

### Getting Help

1. Check this README
2. Review the [Git Hooks Documentation](docs/git-hooks.md)
3. Run the setup script: `./scripts/setup-dev-environment.sh`
4. Check the application logs in the `logs/` directory
5. Ask the development team

## 📚 Documentation

- [Git Hooks Documentation](docs/git-hooks.md) - Detailed information about Git hooks
- [API Documentation](docs/api.md) - API endpoints and usage (if available)
- [Database Schema](docs/database.md) - Database structure (if available)

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** (Git hooks will validate your commits)
4. **Commit your changes**: `git commit -m "Add amazing feature"`
5. **Push to the branch**: `git push origin feature/amazing-feature`
6. **Open a Pull Request**

### Commit Message Guidelines

- Use present tense ("Add feature" not "Added feature")
- Use imperative mood ("Move cursor to..." not "Moves cursor to...")
- Limit the first line to 72 characters or less
- Reference issues and pull requests liberally after the first line

## 📄 License

This project is licensed under the [MIT License](LICENSE) - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues or have questions:

1. Check the troubleshooting section above
2. Search existing issues in the repository
3. Create a new issue with detailed information
4. Contact the development team

---

**Happy coding! 🚀**
