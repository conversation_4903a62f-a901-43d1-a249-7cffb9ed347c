ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_LEEDStatus`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_LEEDStatus`
  FOREIGN KEY (`LEEDStatusID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_EnergyProvider`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_EnergyProvider`
  FOREIGN KEY (`EnergyProviderID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_Building`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_Building`
  FOREIGN KEY (`BuildSpecStatusID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`)
  ON DELETE RESTRICT
  ON UPDATE RESTRICT;

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_ClassType`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_ClassType`
  FOREIGN KEY (`ClassTypeID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_CleanRoomClassID`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_CleanRoomClassID`
  FOREIGN KEY (`CleanRoomClassID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_CondoTypeID`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_CondoTypeID`
  FOREIGN KEY (`CondoTypeID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_ConstructionType`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_ConstructionType`
  FOREIGN KEY (`ConstructionTypeID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_ConstuctionStatus`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_ConstuctionStatus`
  FOREIGN KEY (`ConstructionStatusID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_EnergyStarRatingID`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_EnergyStarRatingID`
  FOREIGN KEY (`EnergyStarRatingID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_GovermentInterest`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_GovermentInterest`
  FOREIGN KEY (`GovernmentInterestID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_GreenStarRatingID`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_GreenStarRatingID`
  FOREIGN KEY (`GreenStarRatingID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_HVAC_OfficeAC`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_HVAC_OfficeAC`
  FOREIGN KEY (`OfficeAC`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_HVAC_Officeheat`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_HVAC_Officeheat`
  FOREIGN KEY (`OfficeHeat`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_HVACType`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_HVACType`
  FOREIGN KEY (`HVACTypeID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_LotSizeSourceID`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_LotSizeSourceID`
  FOREIGN KEY (`LotSizeSourceID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_SizeSource_NRA`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_SizeSource_NRA`
  FOREIGN KEY (`NRASizeSourceID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_SizeSourceID`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_SizeSourceID`
  FOREIGN KEY (`SizeSourceID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_SprinklerType`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_SprinklerType`
  FOREIGN KEY (`SprinklerTypeID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_TenancyType`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_TenancyType`
  FOREIGN KEY (`TenancyTypeID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_Zoning_Potential`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_Zoning_Potential`
  FOREIGN KEY (`PotentialZoningID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_Zoning_Surrounding`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_Zoning_Surrounding`
  FOREIGN KEY (`SurroundingLandUse`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Property` 
DROP FOREIGN KEY `fk_Property_ZoningClassID`;
ALTER TABLE `Empirical_Prod`.`Property` 
ADD CONSTRAINT `fk_Property_ZoningClassID`
  FOREIGN KEY (`ZoningClassID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);
