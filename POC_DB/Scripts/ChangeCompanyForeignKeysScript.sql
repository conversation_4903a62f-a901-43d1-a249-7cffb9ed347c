ALTER TABLE `Empirical_Prod`.`Company`
DROP FOREIGN KEY `fk_comany_ratingtierid`;
ALTER TABLE `Empirical_Prod`.`Company`
ADD CONSTRAINT `fk_comany_ratingtierid`
  FOREIGN KEY (`RatingTierID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`)
  ON DELETE RESTRICT
  ON UPDATE RESTRICT;

ALTER TABLE `Empirical_Prod`.`Company`
DROP FOREIGN KEY `fk_company_floorstatusid`;
ALTER TABLE `Empirical_Prod`.`Company`
ADD CONSTRAINT `fk_company_floorstatusid`
  FOREIGN KEY (`FloorStatusID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Company`
DROP FOREIGN KEY `fk_company_hidereason`;
ALTER TABLE `Empirical_Prod`.`Company`
ADD CONSTRAINT `fk_company_hidereason`
  FOREIGN KEY (`HideReasonID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Company`
DROP FOREIGN KEY `fk_company_tenantresearchstatusid`,
DROP FOREIGN KEY `fk_company_tierid`;
ALTER TABLE `Empirical_Prod`.`Company`
ADD CONSTRAINT `fk_company_tenantresearchstatusid`
  FOREIGN KEY (`TenantResearchStatusID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`),
ADD CONSTRAINT `fk_company_tierid`
  FOREIGN KEY (`CompanyTierID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);
