ALTER TABLE `Empirical_Prod`.`Address`
DROP FOREIGN KEY `fk_address_type_id`;
ALTER TABLE `Empirical_Prod`.`Address`
ADD CONSTRAINT `fk_address_type_id`
  FOREIGN KEY (`AddressTypeID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Address`
DROP FOREIGN KEY `fk_address_prefix`,
DROP FOREIGN KEY `fk_address_prefix2`;
ALTER TABLE `Empirical_Prod`.`Address`
ADD CONSTRAINT `fk_address_prefix`
  FOREIGN KEY (`PrefixID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`),
ADD CONSTRAINT `fk_address_prefix2`
  FOREIGN KEY (`Prefix2ID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`);

ALTER TABLE `Empirical_Prod`.`Address`
DROP FOREIGN KEY `fk_address_suffix`,
DROP FOREIGN KEY `fk_address_suffix2`;
ALTER TABLE `Empirical_Prod`.`Address`
ADD CONSTRAINT `fk_address_suffix`
  FOREIGN KEY (`SuffixID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION,
ADD CONSTRAINT `fk_address_suffix2`
  FOREIGN KEY (`Suffix2ID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

ALTER TABLE `Empirical_Prod`.`Address`
ADD INDEX `fk_address_quadrant_idx` (`QuadrantID` ASC) VISIBLE;
ALTER TABLE `Empirical_Prod`.`Address`
ADD CONSTRAINT `fk_address_quadrant`
  FOREIGN KEY (`QuadrantID`)
  REFERENCES `Empirical_Prod`.`lookup` (`ID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;
